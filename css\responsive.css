/* Object-fit adjustments for different screens */
@media (min-width: 1024px) {
    .hero-image {
        object-fit: cover;
        object-position: center top;
    }
}

/* iPhone SE (1st gen) Portrait Mode (320x568) - Device-width approach */
@media only screen and (device-width: 320px) and (device-height: 568px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 112px !important;
        height: 25vh !important;
        min-height: 128px !important;
        max-height: none !important;
    }

    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 25px;
        height: 25px;
    }
}


/* iPhone 6/7/8 Portrait Mode - Device-width approach (Working) */
@media only screen and (device-width: 375px) and (device-height: 667px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 168px !important;
        height: 25vh !important;
        min-height: 125px !important;
        max-height: none !important;
    }


    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
    }
}


/* iPhone X/XS/11 Pro Portrait Mode (375x812) - Device-width approach */
@media only screen and (device-width: 375px) and (device-height: 812px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 20vh !important;
        min-height: 135px !important;
        max-height: none !important;
    }
    .page-hero {
        margin-top: 116px !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* iPhone 12/13/14 Portrait Mode (390x844) - Non-overlapping range */
@media (min-width: 388px) and (max-width: 392px) and (min-height: 842px) and (max-height: 846px) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 21vh !important;
        min-height: 129px !important;
        max-height: none !important;
    }
    .page-hero {
        margin-top: 116px !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* iPhone 15/15 Pro Portrait Mode (393x852) - Device-width approach */
@media only screen and (device-width: 393px) and (device-height: 852px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 20vh !important;
        min-height: 131px !important;
        max-height: none !important;
    }
    .page-hero {
        margin-top: 116px !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* iPhone 16 Pro Portrait Mode (402x874) - Device-width approach */
@media only screen and (device-width: 402px) and (device-height: 874px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 20vh !important;
        min-height: 133px !important;
        max-height: none !important;
    }
    .page-hero {
        margin-top: 116px !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* iPhone XR/11 Portrait Mode (414x896) - Broader range for better compatibility */
@media (min-width: 412px) and (max-width: 416px) and (min-height: 894px) and (max-height: 898px) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 20vh !important;
        min-height: 129px !important;
        max-height: none !important;
    }
    .page-hero {
        margin-top: 116px !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* Screen 412x915 Portrait Mode - Device-width approach */
@media only screen and (device-width: 412px) and (device-height: 915px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 20vh !important;
        min-height: 130px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* Screen 415x915 Portrait Mode - Device-width approach */
@media only screen and (device-width: 415px) and (device-height: 915px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 20vh !important;
        min-height: 130px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* iPhone 13/14 Pro Max & 15 Plus/Pro Max Portrait Mode (428x926) - Device-width approach */
@media only screen and (device-width: 428px) and (device-height: 926px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 20vh !important;
        min-height: 130px !important;
        max-height: none !important;
    }
    .page-hero {
        margin-top: 116px !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* iPhone 15 Plus/Pro Max & 16 Plus Portrait Mode (430x932) - Device-width approach */
@media only screen and (device-width: 430px) and (device-height: 932px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 20vh !important;
        min-height: 130px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* iPhone 16 Pro Max Portrait Mode (440x956) - Device-width approach */
@media only screen and (device-width: 440px) and (device-height: 956px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 20vh !important;
        min-height: 132px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* Screen 360x740 Portrait Mode - Device-width approach */
@media only screen and (device-width: 360px) and (device-height: 740px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 111px !important;
        height: 20vh !important;
        min-height: 155px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* iPad Portrait Mode (768x1024) - Device-width approach */
@media only screen and (device-width: 768px) and (device-height: 1024px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 119px !important;
        height: 25vh !important;
        min-height: 333px !important;
        max-height: none !important;
    }
    .page-hero {
        margin-top: 119px !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 35px;
        height: 35px;
    }
}

/* iPad Air/Pro Portrait Mode (820x1180) - Device-width approach */
@media only screen and (device-width: 820px) and (device-height: 1180px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 141px !important;
        height: 25vh !important;
        min-height: 347px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
    }
}

/* iPad Pro 12.9" Portrait Mode (1024x1366) - Device-width approach */
@media only screen and (device-width: 1024px) and (device-height: 1366px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 141px !important;
        height: 33vh !important;
        min-height: 368px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
    }
}

/* Surface pro 7 Portrait Mode (912x1368) - Device-width approach */
@media only screen and (device-width: 912px) and (device-height: 1368px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 139px !important;
        height: 25vh !important;
        min-height: 392px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
    }
}
/* Surface Duo Portrait Mode (540x720) - Device-width approach */
@media only screen and (device-width: 540px) and (device-height: 720px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 119px !important;
        height: 20vh !important;
        min-height: 233px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
    }
}

/* Galaxy Z Fold 5 Portrait Mode (344x882) - Device-width approach */
@media only screen and (device-width: 344px) and (device-height: 882px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 111px !important;
        height: 15vh !important;
        min-height: 149px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}
/* Galaxy Z Fold 5 Portrait Mode (853x1280) - Device-width approach */
@media only screen and (device-width: 853px) and (device-height: 1280px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 141px !important;
        height: 25vh !important;
        min-height: 361px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 35px;
        height: 35px;
    }
}

/* Galaxy Z Fold 5 Portrait Mode (412x914) - Device-width approach */
@media only screen and (device-width: 412px) and (device-height: 914px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 20vh !important;
        min-height: 130px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* Nest Hub Portrait Mode (1024x600) - Device-width approach */
@media only screen and (device-width: 1024px) and (device-height: 600px) and (orientation: landscape) {
    .hero-section.hero-section {
        margin-top: 140px !important;
        height: 55vh !important;
        min-height: 443px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
    }
}

/* Desktop/Laptop (1280x800) - Device-width approach */
@media only screen and (device-width: 1280px) and (device-height: 800px) and (orientation: landscape) {
    .hero-section.hero-section {
        margin-top: 142px !important;
        height: 55vh !important;
        min-height: 554px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
    }
}

/* Most Common Laptop (1366x768) - Device-width approach */
@media only screen and (device-width: 1366px) and (device-height: 768px) and (orientation: landscape) {
    .hero-section.hero-section {
        margin-top: 142px !important;
        height: 55vh !important;
        min-height: 591px !important;
        max-height: none !important;
    }
    .page-hero {
        margin-top: 142px !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
    }
}

/* MacBook Air (1440x900)*/
@media only screen and (device-width: 1440px) and (device-height: 900px) and (orientation: landscape) {
    .hero-section.hero-section {
        margin-top: 146px !important;
        height: 55vh !important;
        min-height: 623px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
    }
}
/* Budget laptop (1600x900)*/
@media only screen and (device-width: 1600px) and (device-height: 900px) and (orientation: landscape) {
    .hero-section.hero-section {
        margin-top: 151px !important;
        height: 55vh !important;
        min-height: 692px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
    }
}

/* 15.6" Laptop (1536x864) - 1920x1080 at 125% scaling */
@media only screen and (device-width: 1536px) and (device-height: 864px) and (orientation: landscape) {
    .hero-section.hero-section {
        margin-top: 146px !important;
        height: 65vh !important;
        min-height: 664px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 45px;
        height: 45px;
    }
}

/* Full HD Desktop/Laptop (1920x1080) - Device-width approach */
@media only screen and (device-width: 1920px) and (device-height: 1080px) and (orientation: landscape) {
    .hero-section.hero-section {
        margin-top: 153px !important;
        height: 55vh !important;
        min-height: 831px !important;
        max-height: none !important;
    }
    .page-hero {
        margin-top: 153px !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 60px;
        height: 60px;
    }
}

/* 2K/QHD Monitor (2560x1440) - Device-width approach */
@media only screen and (device-width: 2560px) and (device-height: 1440px) and (orientation: landscape) {
    .hero-section.hero-section {
        margin-top: 142px !important;
        height: 70vh !important;
        min-height: 1107px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 70px;
        height: 70px;
    }
}

/* ========================================
   About Section Responsive Enhancements
   ======================================== */

/* Large Desktop Displays */
@media (min-width: 1400px) {
    .bottom-panels-row {
        gap: 5rem;
        max-width: 1400px;
    }

    .event-message-panel {
        padding: 5rem 4rem;
        max-width: 1400px;
    }

    .message-main-title {
        font-size: 3.5rem;
    }
}

/* Standard Desktop */
@media (min-width: 1200px) and (max-width: 1399px) {
    .bottom-panels-row {
        gap: 4rem;
        max-width: 1200px;
    }

    .event-message-panel {
        max-width: 1200px;
    }
}

/* Small Desktop / Large Tablet Landscape - Including 1024x1366 */
@media (max-width: 1199px) {
    .bottom-panels-row {
        gap: 3rem;
        max-width: 1000px;
    }

    .section-title {
        font-size: 4rem;
    }

    .event-message-panel {
        padding: 3rem 2.5rem;
        max-width: 1000px;
    }

    .quick-links-panel,
    .contact-info-panel {
        padding: 2.5rem 2rem;
    }
}

/* Tablet Landscape - Keep side-by-side for 1024x1366 */
@media (max-width: 1024px) and (min-width: 769px) {
    .about-grid {
        display: flex;
        flex-direction: column;
        gap: 3rem;
    }

    .bottom-panels-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2.5rem;
        max-width: 900px;
    }

    .event-message-panel {
        max-width: 900px;
        padding: 2.5rem 2rem;
    }

    .quick-links-panel,
    .contact-info-panel {
        position: static;
        padding: 2rem 1.5rem;
    }

    .section-title {
        font-size: 3.5rem;
    }

    .about-section {
        padding: 2rem 0;
    }

    .message-header {
        gap: 2rem;
    }

    /* Footer optimizations for 1024x1366 */
    .footer-section {
        padding: 5rem 0 2rem;
        margin-top: 6rem;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 2rem;
        max-width: 1000px;
        margin: 0 auto;
    }

    .footer-brand {
        grid-column: 1 / -1;
        text-align: center;
        margin-bottom: 2rem;
    }

    .footer-description {
        max-width: 600px;
        margin: 0 auto 2rem auto;
        text-align: center;
    }

    .footer-social-links {
        justify-content: center;
    }
}

/* Tablet Portrait - Stack panels vertically */
@media (max-width: 768px) {
    .about-section {
        padding: 2rem 0;
    }

    .section-header {
        margin-bottom: 4rem;
    }

    .section-title {
        font-size: 3rem;
        line-height: 1.1;
    }

    .section-description {
        font-size: 1.6rem;
    }

    .about-grid {
        display: flex;
        flex-direction: column;
        gap: 3rem;
    }

    .bottom-panels-row {
        display: flex;
        flex-direction: column;
        gap: 3rem;
        max-width: 100%;
    }

    .event-message-panel {
        padding: 2.5rem 2rem;
        max-width: 100%;
    }

    .message-header {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .message-logo {
        width: 6rem;
        height: 6rem;
    }

    .message-main-title {
        font-size: 2.5rem;
    }

    .quick-links-panel,
    .contact-info-panel {
        padding: 2.5rem 2rem;
        max-width: 60rem;
        margin: 0 auto;
    }
    
    .quick-contact-actions {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 0.8rem;
    }
    
    .contact-action-btn {
        flex: 1;
        min-width: 0;
        font-size: 1.2rem;
        padding: 1rem 1.5rem;
    }
    
    .secretary-card {
        padding: 2rem 1.5rem;
    }
    
    .secretary-avatar {
        width: 5rem;
        height: 5rem;
    }
    
    .secretary-avatar i {
        font-size: 2rem;
    }
    
    .hosted-by {
        padding: 1.5rem;
    }
}

/* Mobile Landscape */
@media (max-width: 640px) {
    .section-title {
        font-size: 2.8rem;
    }
    
    .message-main-title {
        font-size: 2.3rem;
    }
    
    .quick-link {
        padding: 1.2rem;
        gap: 1rem;
    }
    
    .link-icon {
        width: 3.5rem;
        height: 3.5rem;
    }
    
    .panel-title {
        font-size: 1.8rem;
    }
}

/* Mobile Portrait */
@media (max-width: 480px) {
    .about-section {
        padding: 3rem 0;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .section-description {
        font-size: 1.6rem;
    }

    .about-grid {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }

    .bottom-panels-row {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        max-width: 100%;
    }

    .event-message-panel {
        padding: 2rem 1.5rem;
        border-width: 2px;
        max-width: 100%;
    }

    .message-main-title {
        font-size: 2.2rem;
    }

    .quick-links-panel,
    .contact-info-panel {
        padding: 2rem 1.5rem;
    }
    
    .quick-link {
        padding: 1.2rem;
        gap: 1rem;
    }
    
    .link-icon {
        width: 3.5rem;
        height: 3.5rem;
    }
    
    .link-icon i {
        font-size: 1.6rem;
    }
    
    .link-title {
        font-size: 1.4rem;
    }
    
    .link-subtitle {
        font-size: 1.1rem;
    }
    
    .quick-contact-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    .contact-action-btn {
        font-size: 1.3rem;
    }
    
    .secretary-card {
        padding: 2rem 1.5rem;
    }
    
    .message-paragraph {
        font-size: 1.4rem;
        text-align: left;
    }
    
    .message-paragraph.highlight {
        font-size: 1.5rem;
        padding: 1.5rem;
    }
    
    .organizing-team {
        padding: 1.5rem;
        text-align: center;
    }
    
    .hosted-by {
        padding: 1.5rem;
    }
    
    .hosted-title {
        font-size: 1.2rem;
    }
    
    .hosted-text {
        font-size: 1.4rem;
    }
}

/* Small Mobile Devices - 360px Optimization */
@media (max-width: 360px) {
    .section-title {
        font-size: 2.2rem;
    }

    /* Ensure mobile order is maintained */
    .about-grid {
        display: flex;
        flex-direction: column;
    }

    .event-message-panel {
        order: 1;
        padding: 1.5rem 1rem;
    }

    .quick-links-panel {
        order: 2;
        padding: 1.5rem 1rem;
    }

    .contact-info-panel {
        order: 3;
        padding: 1.5rem 1rem;
    }

    .decoration-line {
        width: 3rem;
    }

    .title-decoration {
        gap: 1rem;
    }

    .message-main-title {
        font-size: 2rem;
    }

    .panel-title {
        font-size: 1.6rem;
    }

    .panel-title i {
        font-size: 1.8rem;
    }

    .link-title {
        font-size: 1.3rem;
    }

    .link-subtitle {
        font-size: 1rem;
    }

    .secretary-name {
        font-size: 1.6rem;
    }

    .secretary-title {
        font-size: 1.2rem;
    }

    .contact-item {
        padding: 0.8rem;
    }

    /* Footer specific optimizations for 360px */
    .footer-section {
        padding: 3rem 0 1.5rem;
        margin-top: 4rem;
    }

    .footer-content {
        padding: 0 1rem;
        gap: 2.5rem;
    }

    .footer-brand {
        margin-bottom: 2rem;
    }

    .footer-logo-container {
        justify-content: center;
        gap: 1.2rem;
    }

    .footer-logo {
        width: 5rem;
        height: 5rem;
    }

    .footer-conference-title {
        font-size: 1.6rem;
        line-height: 1.3;
    }

    .footer-conference-subtitle {
        font-size: 1.2rem;
    }

    .footer-conference-date {
        font-size: 1.1rem;
    }

    .footer-description {
        font-size: 1rem;
        line-height: 1.6;
        max-width: 300px;
        margin: 0 auto 2rem auto;
    }
}
    
    .contact-item span {
        font-size: 1.2rem;
    }
    
    .message-paragraph {
        font-size: 1.3rem;
    }
    
    .carousel-btn {
        width: 3.5rem;
        height: 3.5rem;
    }
    
    .carousel-btn i {
        font-size: 1.2rem;
    }
}

/* High DPI / Retina Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .message-logo,
    .conference-seal-img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Print Styles for About Section */
@media print {
    .about-section {
        background: white !important;
        padding: 2rem 0 !important;
    }
    
    .about-section::before {
        display: none !important;
    }
    
    .about-grid {
        display: block !important;
    }
    
    .quick-links-panel,
    .contact-info-panel {
        display: none !important;
    }
    
    .event-message-panel {
        border: 1px solid #333 !important;
        background: white !important;
        box-shadow: none !important;
        padding: 2rem !important;
    }
    
    .carousel-navigation,
    .quick-contact-actions {
        display: none !important;
    }
    
    .conference-seal-img {
        animation: none !important;
    }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    .about-section *,
    .about-section *::before,
    .about-section *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .secretariat-slide {
        transition: none !important;
    }
    
    .conference-seal-img {
        animation: none !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .event-message-panel {
        border-width: 3px !important;
        border-color: #000 !important;
    }
    
    .secretary-card {
        border-width: 2px !important;
        border-color: #000 !important;
    }
    
    .quick-link {
        border: 2px solid #000 !important;
    }
    
    .contact-item:hover {
        background: #000 !important;
        color: #fff !important;
    }
}

/* Dark Mode Support - DISABLED FOR LIGHT THEME */
@media (prefers-color-scheme: dark) {
    /* Keeping light theme always - no dark mode */
    .about-section {
        background: linear-gradient(135deg, #f7fafc 0%, #ffffff 50%, #f7fafc 100%) !important;
    }
    
    .event-message-panel,
    .quick-links-panel,
    .contact-info-panel,
    .secretary-card {
        background: #ffffff !important;
        border-color: #e2e8f0 !important;
        color: #2d3748 !important;
    }
    
    .message-paragraph,
    .link-title,
    .secretary-name {
        color: #2d3748 !important;
    }
    
    .link-subtitle,
    .contact-item span {
        color: #718096 !important;
    }
}

/* Mobile Fallback - iPhone XR/11 Alternative */
@media only screen and (min-width: 376px) and (max-width: 420px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 116px !important;
        height: 20vh !important;
        min-height: 129px !important;
        max-height: none !important;
    }

    .slider-nav {
        width: 28px;
        height: 28px;
    }
}

/* Note: Additional iPhone models (iPhone SE, iPhone X series, iPhone 12-16 series) 
   are already covered in the device-specific media queries above */

/* Vivo V29e India Portrait Mode (1080x2400) - Device-width approach */
@media only screen and (device-width: 1080px) and (device-height: 2400px) and (orientation: portrait) {
    .hero-section.hero-section {
        margin-top: 144px !important;
        height: 20vh !important;
        min-height: 125px !important;
        max-height: none !important;
    }
    .slider-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 28px;
        height: 28px;
    }
}

/* ========================================
   GLOBAL PAGE HERO RESPONSIVE STYLES
   ======================================== */

/* Base global hero responsive adjustments */
@media (max-width: 1200px) {
    .global-page-hero {
        padding: 6rem 0 4rem;
        margin-top: 12rem !important;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .global-page-hero .container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }
    
    .global-hero-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    .global-hero-title {
        font-size: 4rem;
        letter-spacing: 1.5px;
        line-height: 1.1;
        word-wrap: break-word;
        hyphens: auto;
    }
    
    .global-hero-subtitle {
        font-size: 1.6rem;
        line-height: 1.4;
        padding: 0 1rem;
        word-wrap: break-word;
        text-align: center;
    }
    
    .global-hero-quick-info {
        gap: 2rem;
        flex-wrap: wrap;
        justify-content: center;
    }
}

@media (max-width: 1024px) {
    .global-page-hero {
        padding: 5rem 0 3.5rem;
        margin-top: 11rem !important;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .global-page-hero .container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }
    
    .global-hero-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    .global-hero-title {
        font-size: 3.5rem;
        letter-spacing: 1.2px;
        line-height: 1.1;
        max-width: 90%;
        margin: 0 auto 2rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .global-hero-subtitle {
        font-size: 1.5rem;
        margin-bottom: 2.5rem;
        line-height: 1.4;
        padding: 0 1.5rem;
        max-width: 85%;
        margin-left: auto;
        margin-right: auto;
        word-wrap: break-word;
    }
    
    .global-hero-badge {
        padding: 1.3rem 2.5rem;
        font-size: 1.3rem;
        max-width: 80%;
        margin: 2rem auto;
        word-wrap: break-word;
        text-align: center;
    }
}

@media (max-width: 768px) {
    .global-page-hero {
        padding: 4rem 0 3rem;
        margin-top: 10rem !important;
        min-height: 20vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .global-page-hero .container {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
    }
    
    .global-hero-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
    }
    
    .global-hero-title {
        font-size: 2.8rem;
        letter-spacing: 1px;
        line-height: 1.1;
        max-width: 95%;
        margin: 0 auto 1.5rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        text-align: center;
    }
    
    .global-hero-subtitle {
        font-size: 1.4rem;
        margin-bottom: 2rem;
        line-height: 1.4;
        padding: 0 2rem;
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
        word-wrap: break-word;
        text-align: center;
    }
    
    .global-hero-badge {
        padding: 1.2rem 2rem;
        font-size: 1.2rem;
        max-width: 85%;
        margin: 1.5rem auto;
        word-wrap: break-word;
        text-align: center;
        line-height: 1.3;
    }
    
    .global-hero-quick-info {
        flex-direction: column;
        gap: 1.5rem;
        margin-top: 3rem;
        padding: 0 1rem;
    }
    
    .global-quick-info-item {
        min-width: auto;
        padding: 1rem 1.5rem;
        max-width: 90%;
        margin: 0 auto;
    }
    
    .global-quick-info-text {
        word-wrap: break-word;
        text-align: center;
        line-height: 1.3;
    }
}

@media (max-width: 480px) {
    .global-page-hero {
        padding: 3rem 0 2.5rem;
        margin-top: 9rem !important;
    }
    
    .global-hero-title {
        font-size: 2.2rem;
        margin-bottom: 1.5rem;
        letter-spacing: 0.8px;
        line-height: 1.1;
        max-width: 98%;
        margin-left: auto;
        margin-right: auto;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        text-align: center;
        padding: 0 1rem;
    }
    
    .global-hero-subtitle {
        font-size: 1.2rem;
        margin-bottom: 1.5rem;
        line-height: 1.4;
        padding: 0 1.5rem;
        max-width: 95%;
        margin-left: auto;
        margin-right: auto;
        word-wrap: break-word;
        text-align: center;
    }
    
    .global-hero-badge {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        margin-top: 1.5rem;
        max-width: 90%;
        margin-left: auto;
        margin-right: auto;
        word-wrap: break-word;
        text-align: center;
        line-height: 1.3;
        letter-spacing: 0.5px;
    }
    
    .global-quick-info-item {
        padding: 0.8rem 1.2rem;
        gap: 1rem;
        max-width: 95%;
        margin: 0 auto;
    }
    
    .global-quick-info-icon {
        width: 3rem;
        height: 3rem;
        flex-shrink: 0;
    }
    
    .global-quick-info-icon i {
        font-size: 1.4rem;
    }
    
    .global-quick-info-text {
        font-size: 1.1rem;
        word-wrap: break-word;
        text-align: center;
        line-height: 1.3;
        flex: 1;
    }
}

@media (max-width: 360px) {
    .global-page-hero {
        padding: 2.5rem 0 2rem;
        margin-top: 8rem !important;
    }
    
    .global-hero-title {
        font-size: 1.8rem;
        margin-bottom: 1rem;
        letter-spacing: 0.5px;
        line-height: 1.1;
        max-width: 100%;
        margin-left: auto;
        margin-right: auto;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
        text-align: center;
        padding: 0 0.8rem;
    }
    
    .global-hero-subtitle {
        font-size: 1rem;
        margin-bottom: 1rem;
        line-height: 1.3;
        padding: 0 1rem;
        max-width: 100%;
        margin-left: auto;
        margin-right: auto;
        word-wrap: break-word;
        text-align: center;
    }
    
    .global-hero-badge {
        padding: 0.8rem 1.2rem;
        font-size: 0.9rem;
        margin-top: 1rem;
        max-width: 95%;
        margin-left: auto;
        margin-right: auto;
        word-wrap: break-word;
        text-align: center;
        line-height: 1.3;
        letter-spacing: 0.3px;
    }
    
    .global-quick-info-item {
        padding: 0.6rem 1rem;
        gap: 0.8rem;
        max-width: 100%;
        margin: 0 auto;
        flex-direction: column;
        text-align: center;
    }
    
    .global-quick-info-icon {
        width: 2.5rem;
        height: 2.5rem;
        margin: 0 auto;
        flex-shrink: 0;
    }
    
    .global-quick-info-icon i {
        font-size: 1.2rem;
    }
    
    .global-quick-info-text {
        font-size: 1rem;
        word-wrap: break-word;
        text-align: center;
        line-height: 1.3;
        margin-top: 0.5rem;
    }
}

/* Device-specific adjustments for common phones and tablets */

/* iPhone SE (1st gen) Portrait Mode */
@media only screen and (device-width: 320px) and (device-height: 568px) and (orientation: portrait) {
    .global-page-hero {
        margin-top: 112px !important;
        padding: 2rem 0 1.5rem;
        min-height: 15vh;
    }
}

/* iPhone 6/7/8 Portrait Mode */
@media only screen and (device-width: 375px) and (device-height: 667px) and (orientation: portrait) {
    .global-page-hero {
        margin-top: 168px !important;
        padding: 2.5rem 0 2rem;
        min-height: 18vh;
    }
}

/* iPhone X/XS/11 Pro Portrait Mode */
@media only screen and (device-width: 375px) and (device-height: 812px) and (orientation: portrait) {
    .global-page-hero {
        margin-top: 116px !important;
        padding: 3rem 0 2.5rem;
        min-height: 18vh;
    }
}

/* iPhone 12/13/14 Portrait Mode */
@media (min-width: 388px) and (max-width: 392px) and (min-height: 842px) and (max-height: 846px) {
    .global-page-hero {
        margin-top: 116px !important;
        padding: 3rem 0 2.5rem;
        min-height: 18vh;
    }
}

/* iPhone 15/15 Pro Portrait Mode */
@media only screen and (device-width: 393px) and (device-height: 852px) and (orientation: portrait) {
    .global-page-hero {
        margin-top: 116px !important;
        padding: 3rem 0 2.5rem;
        min-height: 18vh;
    }
}

/* iPhone XR/11 Portrait Mode */
@media (min-width: 412px) and (max-width: 416px) and (min-height: 894px) and (max-height: 898px) {
    .global-page-hero {
        margin-top: 116px !important;
        padding: 3rem 0 2.5rem;
        min-height: 18vh;
    }
}

/* iPhone 13/14 Pro Max & 15 Plus/Pro Max Portrait Mode */
@media only screen and (device-width: 428px) and (device-height: 926px) and (orientation: portrait) {
    .global-page-hero {
        margin-top: 116px !important;
        padding: 3rem 0 2.5rem;
        min-height: 18vh;
    }
}

/* iPad Portrait Mode */
@media only screen and (device-width: 768px) and (device-height: 1024px) and (orientation: portrait) {
    .global-page-hero {
        margin-top: 119px !important;
        padding: 4rem 0 3rem;
        min-height: 22vh;
    }
}

/* Desktop Devices */
@media only screen and (device-width: 1366px) and (device-height: 768px) and (orientation: landscape) {
    .global-page-hero {
        margin-top: 142px !important;
        padding: 8rem 0 6rem;
        min-height: 30vh;
    }
}

@media only screen and (device-width: 1920px) and (device-height: 1080px) and (orientation: landscape) {
    .global-page-hero {
        margin-top: 153px !important;
        padding: 10rem 0 8rem;
        min-height: 35vh;
    }
}

/* High resolution displays */
@media (min-width: 1600px) {
    .global-page-hero {
        padding: 10rem 0 8rem;
        margin-top: 16rem !important;
    }
    
    .global-hero-title {
        font-size: 5rem;
        letter-spacing: 2.5px;
        max-width: 85%;
    }
    
    .global-hero-subtitle {
        font-size: 2rem;
        max-width: 70%;
    }
    
    .global-hero-badge {
        padding: 1.8rem 3.5rem;
        font-size: 1.5rem;
        max-width: 60%;
    }
}

/* Extra small devices (less than 320px) */
@media (max-width: 320px) {
    .global-page-hero {
        padding: 2rem 0 1.5rem;
        margin-top: 7rem !important;
    }
    
    .global-hero-title {
        font-size: 1.6rem;
        margin-bottom: 0.8rem;
        letter-spacing: 0.3px;
        line-height: 1.1;
        padding: 0 0.5rem;
    }
    
    .global-hero-subtitle {
        font-size: 0.9rem;
        margin-bottom: 0.8rem;
        line-height: 1.3;
        padding: 0 0.8rem;
    }
    
    .global-hero-badge {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
        margin-top: 0.8rem;
        letter-spacing: 0.2px;
    }
    
    .global-quick-info-item {
        padding: 0.5rem 0.8rem;
        gap: 0.6rem;
    }
    
    .global-quick-info-icon {
        width: 2rem;
        height: 2rem;
    }
    
    .global-quick-info-icon i {
        font-size: 1rem;
    }
    
    .global-quick-info-text {
        font-size: 0.9rem;
        margin-top: 0.3rem;
    }
}

/* Ultra small devices (Samsung Galaxy Fold closed, etc.) */
@media (max-width: 280px) {
    .global-page-hero {
        padding: 1.5rem 0 1rem;
        margin-top: 6rem !important;
    }
    
    .global-hero-title {
        font-size: 1.4rem;
        margin-bottom: 0.6rem;
        letter-spacing: 0.2px;
        padding: 0 0.3rem;
    }
    
    .global-hero-subtitle {
        font-size: 0.8rem;
        margin-bottom: 0.6rem;
        padding: 0 0.5rem;
    }
    
    .global-hero-badge {
        padding: 0.5rem 0.8rem;
        font-size: 0.7rem;
        margin-top: 0.6rem;
        letter-spacing: 0.1px;
    }
    
    .global-quick-info-item {
        padding: 0.4rem 0.6rem;
        gap: 0.4rem;
    }
    
    .global-quick-info-icon {
        width: 1.8rem;
        height: 1.8rem;
    }
    
    .global-quick-info-icon i {
        font-size: 0.9rem;
    }
    
    .global-quick-info-text {
        font-size: 0.8rem;
        margin-top: 0.2rem;
    }
}

/* Mobile Landscape Orientations */
@media (max-height: 500px) and (orientation: landscape) {
    .global-page-hero {
        padding: 2rem 0 1.5rem;
        margin-top: 8rem !important;
        min-height: auto;
    }
    
    .global-hero-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .global-hero-subtitle {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }
    
    .global-hero-badge {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
        margin-top: 1rem;
    }
    
    .global-hero-quick-info {
        margin-top: 1.5rem;
        gap: 1rem;
    }
}

/* Very small landscape (like Galaxy Fold landscape) */
@media (max-height: 350px) and (orientation: landscape) {
    .global-page-hero {
        padding: 1.5rem 0 1rem;
        margin-top: 6rem !important;
    }
    
    .global-hero-title {
        font-size: 1.6rem;
        margin-bottom: 0.8rem;
    }
    
    .global-hero-subtitle {
        font-size: 1rem;
        margin-bottom: 0.8rem;
    }
    
    .global-hero-badge {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
        margin-top: 0.8rem;
    }
}

/* Text selection and accessibility improvements */
.global-page-hero *::selection {
    background: rgba(255, 140, 66, 0.3);
    color: white;
}

.global-page-hero *::-moz-selection {
    background: rgba(255, 140, 66, 0.3);
    color: white;
}

/* Focus states for accessibility */
.global-hero-badge:focus,
.global-quick-info-item:focus {
    outline: 2px solid rgba(255, 140, 66, 0.8);
    outline-offset: 2px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    .global-hero-title {
        animation: none;
    }
    
    .global-hero-badge i {
        animation: none;
    }
    
    .global-page-hero::before {
        animation: none;
    }
}
