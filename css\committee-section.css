/* Committee Section Styles - Pattern Impact Design */
.committee-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 6rem 0;
    position: relative;
    overflow: hidden;
}

.committee-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 50px,
            rgba(59, 130, 246, 0.02) 50px,
            rgba(59, 130, 246, 0.02) 100px
        ),
        radial-gradient(circle at 20% 80%, rgba(249, 115, 22, 0.03) 0%, transparent 50%);
    z-index: 1;
}

.committee-section .container {
    position: relative;
    z-index: 2;
}

/* Stylish <PERSON>er */
.committee-header {
    text-align: center;
    margin-bottom: 5rem;
    position: relative;
}

.committee-header::before {
    content: '';
    position: absolute;
    top: -3rem;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 120px;
    background: 
        conic-gradient(from 0deg, var(--primary-blue), var(--primary-orange), var(--primary-blue));
    border-radius: 50%;
    opacity: 0.1;
    animation: rotate 20s linear infinite;
}

.committee-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--primary-blue) 0%, #1e40af 50%, var(--primary-orange) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1.5rem;
    /* text-transform: uppercase; */
    letter-spacing: 2px;
    position: relative;
    display: inline-block;
}

.committee-title::after {
    content: '';
    position: absolute;
    bottom: -1.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-orange), var(--primary-blue));
    border-radius: 2px;
    box-shadow: 0 2px 10px rgba(59, 130, 246, 0.3);
}

.committee-subtitle {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    margin-top: 2.5rem;
    max-width: 65rem;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.7;
}

/* Conference Highlight Styling */
.conference-highlight {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-orange) 50%, #1e40af 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 1px;
    display: inline-block;
}

/* Grid Layout with Consistent Sizing */
.committee-grid-row1 {
    display: grid;
    grid-template-columns: repeat(4, 280px);
    gap: 2rem;
    margin-bottom: 3rem;
    justify-content: center;
}

.committee-grid-row2 {
    display: grid;
    grid-template-columns: repeat(3, 280px);
    gap: 4rem;
    margin-bottom: 4rem;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
}

.row-center-wrapper {
    display: flex;
    justify-content: center;
    width: 100%;
}

/* Consistent Card Dimensions */
.committee-grid-row1 .committee-member,
.committee-grid-row2 .committee-member {
    width: 280px;
    min-width: 280px;
    max-width: 280px;
    height: 28rem;
    min-height: 26rem;
    display: flex;
    flex-direction: column;
}

/* Pattern Cards */
.committee-member {
    background: var(--white);
    border-radius: 1.5rem;
    box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.08),
        0 5px 15px rgba(0, 0, 0, 0.04);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(255, 255, 255, 0.9);
    overflow: hidden;
    position: relative;
    padding-bottom: 1.5rem;
}

/* Decorative Pattern Overlay */
.committee-member::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: 
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 20px,
            rgba(59, 130, 246, 0.02) 20px,
            rgba(59, 130, 246, 0.02) 40px
        );
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.committee-member:hover::before {
    opacity: 1;
}

/* Top Pattern Border */
.committee-member::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: 
        linear-gradient(90deg, 
            var(--primary-blue) 0%, 
            var(--primary-orange) 25%, 
            var(--primary-blue) 50%, 
            var(--primary-orange) 75%, 
            var(--primary-blue) 100%
        );
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 3;
}

.committee-member:hover::after {
    opacity: 1;
}

.committee-member:hover {
    transform: translateY(-0.3rem);
    box-shadow: 
        0 15px 30px rgba(0, 0, 0, 0.08),
        0 5px 15px rgba(0, 0, 0, 0.06);
    border-color: rgba(59, 130, 246, 0.15);
}

/* Pattern Image Section */
.member-image-section {
    height: 18rem;
    background: 
        radial-gradient(circle at 30% 70%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(249, 115, 22, 0.03) 0%, transparent 50%),
        linear-gradient(135deg, var(--gray-50), var(--gray-25));
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 1rem;
    overflow: hidden;
    flex-shrink: 0;
}

/* Decorative Pattern Elements */
.member-image-section::before {
    content: '';
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 60px;
    height: 60px;
    background: 
        conic-gradient(from 45deg, var(--primary-blue), transparent, var(--primary-orange), transparent);
    border-radius: 50%;
    opacity: 0.1;
    animation: rotate 15s linear infinite reverse;
}

.member-image-section::after {
    content: '';
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    width: 40px;
    height: 40px;
    background: 
        linear-gradient(45deg, var(--primary-orange), var(--primary-blue));
    clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    opacity: 0.1;
    animation: pulse 4s ease-in-out infinite;
}

.member-image-container {
    width: 11rem;
    height: 11rem;
    position: relative;
    transition: all 0.3s ease;
    z-index: 2;
}

/* Hexagonal Pattern Frame */
.member-image-container::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: 
        conic-gradient(from 0deg, 
            var(--primary-blue), 
            var(--primary-orange), 
            var(--primary-blue), 
            var(--primary-orange), 
            var(--primary-blue)
        );
    border-radius: 50%;
    z-index: 1;
    opacity: 0.8;
    animation: rotate 8s linear infinite;
}

.member-image-container::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--white);
    border-radius: 50%;
    z-index: 2;
}

.committee-member:hover .member-image-container {
    transform: scale(1.03);
}

.committee-member:hover .member-image-container::before {
    animation-duration: 4s;
    opacity: 1;
}

.member-image {
    width: 11rem;
    height: 11rem;
    border-radius: 50%;
    object-fit: cover;
    position: relative;
    z-index: 3;
    transition: all 0.3s ease;
}

.committee-member:hover .member-image {
    transform: scale(1.02);
}

/* Pattern Content Section */
.member-content-section {
    height: 8rem;
    padding: 1.5rem;
    text-align: center;
    background: var(--white);
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex-shrink: 0;
}

/* Decorative Corner Pattern */
.member-content-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 80px;
    height: 80px;
    background: 
        repeating-conic-gradient(
            from 0deg,
            transparent 0deg,
            rgba(59, 130, 246, 0.03) 45deg,
            transparent 90deg
        );
    border-radius: 0 0 0 100%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.committee-member:hover .member-content-section::before {
    opacity: 1;
}

.member-name {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-orange) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.6rem;
    line-height: 1.2;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.member-name::after {
    content: '';
    position: absolute;
    bottom: -0.8rem;
    left: 50%;
    transform: translateX(-50%) scaleX(0);
    width: 60%;
    height: 3px;
    background: 
        linear-gradient(90deg, 
            var(--primary-orange), 
            var(--primary-blue), 
            var(--primary-orange)
        );
    transition: transform 0.4s ease;
    transform-origin: center;
    border-radius: 2px;
}

.committee-member:hover .member-name::after {
    transform: translateX(-50%) scaleX(1);
}

.member-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    line-height: 1.4;
    margin: 0;
    margin-top: 0.8rem;
}

/* Pattern Secretariat Section */
.secretariat-section {
    margin-top: 3rem;
    padding-top: 3rem;
    position: relative;
}

.secretariat-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: 
        repeating-linear-gradient(
            90deg,
            var(--primary-blue) 0px,
            var(--primary-blue) 20px,
            var(--primary-orange) 20px,
            var(--primary-orange) 40px,
            var(--primary-blue) 40px,
            var(--primary-blue) 60px,
            transparent 60px,
            transparent 80px
        );
    background-size: 160px 100%;
    animation: slide 6s linear infinite;
}

.secretariat-header {
    text-align: center;
    margin-bottom: 3rem;
    position: relative;
}

.secretariat-header::before {
    content: '';
    position: absolute;
    top: -2rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    background: 
        repeating-conic-gradient(
            from 0deg,
            var(--primary-blue) 0deg,
            var(--primary-blue) 30deg,
            var(--primary-orange) 30deg,
            var(--primary-orange) 60deg
        );
    border-radius: 50%;
    opacity: 0.1;
    animation: rotate 12s linear infinite;
}

.secretariat-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-orange) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    display: inline-block;
}

.secretariat-title::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-orange));
    border-radius: 2px;
}

.secretariat-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    max-width: 75rem;
    margin: 0 auto;
    justify-content: center;
}

/* Pattern Secretariat Cards */
.secretariat-member {
    background: var(--white);
    border-radius: 1.2rem;
    padding: 3rem 1.5rem 2.5rem 1.5rem;
    text-align: center;
    box-shadow: 
        0 12px 30px rgba(0, 0, 0, 0.08),
        0 4px 12px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    position: relative;
    width: 100%;
    max-width: 22rem;
    margin: 0 auto;
    overflow: hidden;
}

/* Geometric Pattern Background */
.secretariat-member::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        repeating-linear-gradient(
            30deg,
            transparent,
            transparent 15px,
            rgba(59, 130, 246, 0.02) 15px,
            rgba(59, 130, 246, 0.02) 30px
        );
    opacity: 0;
    transition: opacity 0.3s ease;
}

.secretariat-member:hover::before {
    opacity: 1;
}

/* Top Diamond Pattern */
.secretariat-member::after {
    content: '';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    background: linear-gradient(45deg, var(--primary-blue), var(--primary-orange));
    clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    transition: all 0.3s ease;
}

.secretariat-member:hover::after {
    top: -20px;
    width: 40px;
    height: 40px;
}

.secretariat-member:hover {
    transform: translateY(-0.2rem);
    box-shadow: 
        0 12px 25px rgba(0, 0, 0, 0.08),
        0 4px 12px rgba(0, 0, 0, 0.06);
}

.secretariat-image-container {
    width: 7.5rem;
    height: 7.5rem;
    margin: 0 auto 2rem;
    position: relative;
    transition: all 0.3s ease;
    z-index: 2;
}

/* Octagonal Pattern Frame */
.secretariat-image-container::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: 
        conic-gradient(from 45deg, 
            var(--primary-blue), 
            var(--primary-orange), 
            var(--primary-blue), 
            var(--primary-orange)
        );
    border-radius: 50%;
    z-index: 1;
    opacity: 0.8;
    animation: rotate 10s linear infinite reverse;
}

.secretariat-image-container::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: var(--white);
    border-radius: 50%;
    z-index: 2;
}

.secretariat-member:hover .secretariat-image-container {
    transform: scale(1.05);
}

.secretariat-member:hover .secretariat-image-container::before {
    animation-duration: 5s;
    opacity: 1;
}

.secretariat-image {
    width: 7.5rem;
    height: 7.5rem;
    border-radius: 50%;
    object-fit: cover;
    position: relative;
    z-index: 3;
    transition: all 0.3s ease;
}

.secretariat-member:hover .secretariat-image {
    transform: scale(1.02);
}

.secretariat-name {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-orange) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    padding-top: 0.5rem;
    line-height: 1.3;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.secretariat-name::after {
    content: '';
    position: absolute;
    bottom: -0.8rem;
    left: 50%;
    transform: translateX(-50%) scaleX(0);
    width: 70%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-orange), var(--primary-blue));
    transition: transform 0.4s ease;
    transform-origin: center;
    border-radius: 1px;
}

.secretariat-member:hover .secretariat-name::after {
    transform: translateX(-50%) scaleX(1);
}

/* Animations */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes shimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes slide {
    0% { background-position: 0% 0%; }
    100% { background-position: 160px 0%; }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.1; }
    50% { transform: scale(1.2); opacity: 0.2; }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .committee-grid-row1 {
        grid-template-columns: repeat(3, 280px);
        gap: 2rem;
        justify-content: center;
    }
    
    .committee-grid-row2 {
        grid-template-columns: repeat(3, 280px);
        gap: 2rem;
        justify-content: center;
    }
    
    .secretariat-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2.5rem;
    }
    
    .member-image-section {
        height: 18rem;
        padding: 1.5rem;
    }
    
    .member-image-container {
        width: 10rem;
        height: 10rem;
    }
    
    .member-image {
        width: 10rem;
        height: 10rem;
    }
    
    /* Chief Patron Section Responsive */
    .chief-patron-section {
        padding: 0 1rem;
        margin-bottom: 5rem;
    }
}

@media (max-width: 1024px) {
    .committee-grid-row1 {
        grid-template-columns: repeat(2, 280px);
        gap: 2.5rem;
        justify-content: center;
    }
    
    .committee-grid-row2 {
        /* grid-template-columns: repeat(2, 280px); */
        gap: 2.5rem;
        justify-content: center;
    }
    
    .secretariat-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2.5rem;
    }
    
    .member-image-section {
        height: 18rem;
        padding: 1.5rem;
    }
    
    .member-image-container {
        width: 10rem;
        height: 10rem;
    }
    
    .member-image {
        width: 10rem;
        height: 10rem;
    }
    
    /* Chief Patron Section Responsive */
    .chief-patron-section {
        padding: 0 1.5rem;
        margin-bottom: 4rem;
    }
}

@media (max-width: 768px) and (min-width: 541px) {
    .committee-section {
        padding: 4rem 0;
    }
    
    .committee-title {
        font-size: var(--font-size-2xl);
    }
    
    .committee-subtitle {
        font-size: var(--font-size-base);
        padding: 0 2rem;
    }
    
    .committee-grid-row2 {
        display: grid !important;
        grid-template-columns: repeat(2, minmax(280px, 320px)) !important;
        gap: 2rem !important;
        padding: 0 2rem;
        justify-content: center !important;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 4rem;
    }
    
    .committee-grid-row2 .committee-member {
        width: 100% !important;
        max-width: 320px !important;
        min-width: 280px !important;
        height: auto;
        min-height: 28rem;
        max-height: none;
        margin: 0 auto !important;
        display: flex;
        flex-direction: column;
        padding-bottom: 2rem;
    }
    
    .member-image-section {
        height: 20rem;
        min-height: 20rem;
        max-height: 20rem;
        padding: 1.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
    }
    
    .member-content-section {
        height: auto;
        min-height: 10rem;
        max-height: 13rem;
        padding: 2rem 1.5rem 2.5rem 1.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        flex-shrink: 0;
        overflow: hidden;
    }
    
    .member-name {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-bold);
        line-height: 1.3;
        margin-bottom: 0.8rem;
        word-wrap: break-word;
        hyphens: auto;
        width: 100%;
        max-width: 100%;
    }
    
    .member-title {
        font-size: var(--font-size-sm);
        line-height: 1.4;
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
        word-wrap: break-word;
        hyphens: auto;
        width: 100%;
        max-width: 100%;
        margin-bottom: 1rem;
    }
    
    .secretariat-grid {
        grid-template-columns: 1fr;
        gap: 2.5rem;
        padding: 0 2rem;
        max-width: none;
    }
    
    .secretariat-member {
        max-width: 350px;
        margin: 0 auto;
        padding: 2.5rem 2rem;
    }
    
    .secretariat-image-container {
        width: 8rem;
        height: 8rem;
    }
    
    .secretariat-image {
        width: 8rem;
        height: 8rem;
    }
    
    /* Chief Patron Section Responsive */
    .chief-patron-section {
        padding: 0 2rem;
        margin-bottom: 4rem;
    }
    
    .chief-patron-card {
        width: 100% !important;
        max-width: 350px !important;
        min-width: 320px !important;
        margin: 0 auto !important;
        transform: scale(1.05) !important;
    }
    
    .chief-patron-card:hover {
        transform: scale(1.05) translateY(-3px) !important;
    }
}

@media (max-width: 540px) and (min-width: 481px) {
    .committee-section {
        padding: 3.5rem 0;
    }
    
    .committee-title {
        font-size: var(--font-size-xl);
    }
    
    .committee-subtitle {
        font-size: var(--font-size-base);
        padding: 0 1.5rem;
    }
    
    .committee-grid-row2 {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2.5rem;
        padding: 0 1.5rem;
        margin-bottom: 4rem;
    }
    
    .committee-grid-row2 .committee-member {
        width: 100%;
        max-width: 350px;
        min-width: 300px;
        height: auto;
        min-height: 28rem;
        max-height: none;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        padding-bottom: 2rem;
    }
    
    .member-image-section {
        height: 20rem;
        min-height: 20rem;
        padding: 1.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
    }
    
    .member-content-section {
        height: auto;
        min-height: 10rem;
        padding: 2rem 1.5rem 2.5rem 1.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        flex-shrink: 0;
        overflow: visible;
    }
    
    .member-name {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-bold);
        line-height: 1.3;
        margin-bottom: 0.8rem;
        word-wrap: break-word;
        hyphens: auto;
        width: 100%;
    }
    
    .member-title {
        font-size: var(--font-size-sm);
        line-height: 1.4;
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
        word-wrap: break-word;
        hyphens: auto;
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .secretariat-grid {
        grid-template-columns: 1fr;
        gap: 2.5rem;
        padding: 0 1.5rem;
    }
    
    .secretariat-member {
        max-width: 350px;
        margin: 0 auto;
        padding: 2.5rem 2rem;
    }
    
    /* Chief Patron Section Responsive */
    .chief-patron-section {
        padding: 0 1.5rem;
        margin-bottom: 4rem;
    }
    
    .chief-patron-card {
        width: 100% !important;
        max-width: 350px !important;
        min-width: 300px !important;
        margin: 0 auto !important;
        transform: scale(1.03) !important;
    }
    
    .chief-patron-card:hover {
        transform: scale(1.03) translateY(-3px) !important;
    }
}

@media (max-width: 480px) {
    .committee-section {
        padding: 3rem 0;
    }
    
    .committee-title {
        font-size: var(--font-size-xl);
        letter-spacing: 1px;
    }
    
    .committee-grid-row2 {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2.5rem;
        padding: 0 1.5rem;
        margin-bottom: 4rem;
    }
    
    .committee-grid-row2 .committee-member {
        width: 100%;
        max-width: 300px;
        min-width: 280px;
        height: auto;
        min-height: 26rem;
        max-height: 30rem;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        padding-bottom: 1.8rem;
    }
    
    .member-image-section {
        height: 18rem;
        min-height: 18rem;
        max-height: 18rem;
        padding: 1rem;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
    }
    
    .member-image-container {
        width: 9rem;
        height: 9rem;
    }
    
    .member-image {
        width: 9rem;
        height: 9rem;
    }
    
    .member-content-section {
        height: auto;
        min-height: 9rem;
        max-height: 13rem;
        padding: 1.5rem 1rem 2rem 1rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        flex-shrink: 0;
        overflow: hidden;
    }
    
    .member-name {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-bold);
        line-height: 1.3;
        margin-bottom: 0.7rem;
        word-wrap: break-word;
        hyphens: auto;
        width: 100%;
        max-width: 100%;
    }
    
    .member-title {
        font-size: var(--font-size-sm);
        line-height: 1.4;
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
        word-wrap: break-word;
        hyphens: auto;
        width: 100%;
        max-width: 100%;
        margin-bottom: 1rem;
    }
    
    .secretariat-grid {
        padding: 0 1.5rem;
    }
    
    .secretariat-member {
        max-width: 300px;
        margin: 0 auto;
        padding: 2rem 1.5rem;
    }
    
    .secretariat-image-container {
        width: 7rem;
        height: 7rem;
    }
    
    .secretariat-image {
        width: 7rem;
        height: 7rem;
    }
    
    .secretariat-name {
        font-size: var(--font-size-sm);
    }
    
    /* Chief Patron Section Responsive */
    .chief-patron-section {
        padding: 0 1.5rem;
        margin-bottom: 3rem;
    }
    
    .chief-patron-card {
        width: 100% !important;
        max-width: 300px !important;
        min-width: 280px !important;
        margin: 0 auto !important;
        transform: scale(1.02) !important;
    }
    
    .chief-patron-card:hover {
        transform: scale(1.02) translateY(-2px) !important;
    }
}

/* Extra Small Screens - Ensure consistency */
@media (max-width: 360px) {
    .committee-grid-row2 {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2rem;
        padding: 0 1rem;
        margin-bottom: 3rem;
    }
    
    .committee-grid-row2 .committee-member {
        width: 100%;
        max-width: 280px;
        min-width: 260px;
        height: 26rem;
        min-height: 26rem;
        max-height: 26rem;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        padding-bottom: 1.5rem;
    }
    
    .member-image-section {
        height: 16rem;
        min-height: 16rem;
        max-height: 16rem;
        padding: 0.8rem;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
    }
    
    .member-image-container {
        width: 8rem;
        height: 8rem;
    }
    
    .member-image {
        width: 8rem;
        height: 8rem;
    }
    
    .member-content-section {
        height: auto;
        min-height: 8rem;
        max-height: 11rem;
        padding: 1.2rem 0.8rem 1.5rem 0.8rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        flex-shrink: 0;
        overflow: hidden;
    }
    
    .member-name {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-bold);
        line-height: 1.2;
        margin-bottom: 0.6rem;
        word-wrap: break-word;
        hyphens: auto;
        width: 100%;
        max-width: 100%;
    }
    
    .member-title {
        font-size: var(--font-size-xs);
        line-height: 1.3;
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
        word-wrap: break-word;
        hyphens: auto;
        width: 100%;
        max-width: 100%;
        margin-bottom: 0.8rem;
    }
    
    .secretariat-member {
        max-width: 280px;
        margin: 0 auto;
        padding: 1.8rem 1.2rem;
    }
    
    .secretariat-image-container {
        width: 6rem;
        height: 6rem;
    }
    
    .secretariat-image {
        width: 6rem;
        height: 6rem;
    }
    
    .secretariat-name {
        font-size: var(--font-size-xs);
    }
    
    /* Chief Patron Section Responsive */
    .chief-patron-section {
        padding: 0 1rem;
        margin-bottom: 3rem;
    }
    
    .chief-patron-card {
        width: 100% !important;
        max-width: 280px !important;
        min-width: 260px !important;
        margin: 0 auto !important;
        transform: scale(1.0) !important;
    }
    
    .chief-patron-card:hover {
        transform: scale(1.0) translateY(-2px) !important;
    }
} 