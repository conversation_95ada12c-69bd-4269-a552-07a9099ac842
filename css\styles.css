/* Import Hero Section Styles */
@import url('hero.css');
/* Import Registration Section Styles */
@import url('registration-section.css');

/* ========================================
   CSS Reset and Base Styles
   ======================================== */

/* Modern CSS Reset */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 62.5%; /* 1rem = 10px for easy calculations */
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--white);
    overflow-x: hidden;
    padding-top: 0;
    min-height: 100vh;
}

/* Remove content spacing after hero section to connect sections smoothly */
.hero-section ~ * {
    margin-top: 0;
}

/* ========================================
   CSS Custom Properties (IMA Logo Color Scheme)
   ======================================== */

:root {
    /* Primary IMA Logo Colors - Exact Match */
    --primary-blue: #2E5990;           /* IMA Logo Blue */
    --primary-blue-light: #4A7BC8;    /* Light IMA Blue */
    --primary-blue-dark: #1A3A5C;     /* Dark IMA Blue */

    /* IMA Logo Orange Colors */
    --primary-orange: #FF8C42;        /* IMA Logo Light Orange */
    --primary-orange-light: #FFB366;  /* Lighter Orange */
    --primary-orange-dark: #E6652A;   /* Darker Orange */

    /* IMA Logo Green Colors */
    --primary-green: #4CAF50;         /* IMA Logo Green */
    --primary-green-light: #66BB6A;   /* Light IMA Green */
    --primary-green-dark: #388E3C;    /* Dark IMA Green */

    /* Secondary Modern Colors */
    --secondary-green: #38A169;       /* Modern Medical Green */
    --secondary-green-light: #48BB78; /* Fresh Medical Green */
    --secondary-green-dark: #2F855A;  /* Deep Medical Green */

    /* Modern Accent Colors */
    --accent-red: #E53E3E;            /* Modern Medical Red */
    --accent-red-light: #FC8181;     /* Light Medical Red */
    --accent-orange: var(--primary-orange); /* Use IMA Orange */
    --accent-gold: #D69E2E;           /* Modern Medical Gold */

    /* Modern Professional Grays */
    --white: #FFFFFF;
    --gray-50: #F7FAFC;
    --gray-100: #EDF2F7;
    --gray-200: #E2E8F0;
    --gray-300: #CBD5E0;
    --gray-400: #A0AEC0;
    --gray-500: #718096;
    --gray-600: #4A5568;
    --gray-700: #2D3748;
    --gray-800: #1A202C;
    --gray-900: #171923;

    /* Semantic Colors */
    --text-primary: var(--gray-800);
    --text-secondary: var(--gray-600);
    --text-light: var(--gray-500);
    --bg-primary: var(--white);
    --bg-secondary: var(--gray-50);
    --border-color: var(--gray-200);
    
    /* Medical Specific Colors */
    --success-color: var(--primary-green);
    --warning-color: var(--primary-orange);
    --error-color: var(--accent-red);
    --info-color: var(--primary-blue-light);

    /* IMA Logo Based Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 100%);
    --gradient-orange: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-orange-light) 100%);
    --gradient-accent: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-green-dark) 100%);
    --gradient-modern: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-orange) 50%, var(--primary-green) 100%);
    --gradient-success: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 100%);
    --gradient-vibrant: linear-gradient(135deg, var(--primary-orange) 0%, var(--primary-blue) 100%);

    /* Modern Shadows */
    --shadow-sm: 0 1px 3px 0 rgba(46, 89, 144, 0.1), 0 1px 2px 0 rgba(46, 89, 144, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(46, 89, 144, 0.1), 0 2px 4px -1px rgba(46, 89, 144, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(46, 89, 144, 0.1), 0 4px 6px -2px rgba(46, 89, 144, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(46, 89, 144, 0.1), 0 10px 10px -5px rgba(46, 89, 144, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(46, 89, 144, 0.25);

    /* Modern Transitions */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

    /* Modern Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 50%;

    /* Modern Typography */
    --font-family-primary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    /* Consistent Font Sizes */
    --font-size-xs: 1.2rem;        /* 12px - Small text */
    --font-size-sm: 1.4rem;        /* 14px - Body small */
    --font-size-base: 1.6rem;      /* 16px - Base body text */
    --font-size-md: 1.8rem;        /* 18px - Large body text */
    --font-size-lg: 2rem;          /* 20px - Small headings */
    --font-size-xl: 2.4rem;        /* 24px - Medium headings */
    --font-size-2xl: 3rem;         /* 30px - Large headings */
    --font-size-3xl: 3.6rem;       /* 36px - Extra large headings */
    --font-size-4xl: 4.8rem;       /* 48px - Hero text */
    --font-size-5xl: 6rem;         /* 60px - Display text */

    /* Container Sizes */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
}

/* ========================================
   Utility Classes
   ======================================== */

.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 2rem;
}

@media (min-width: 1536px) {
    .container {
        max-width: var(--container-2xl);
    }
}

/* Modern Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1.2rem 2.5rem;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--radius-xl);
    cursor: pointer;
    transition: all var(--transition-normal);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-md);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    border-color: var(--primary-blue);
    position: relative;
    z-index: 1;
}

.btn-primary:hover {
    background: var(--gradient-accent);
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-2xl);
    color: var(--white);
    text-decoration: none;
}

.btn-primary:active {
    transform: translateY(-1px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: var(--white);
    color: var(--primary-blue);
    border-color: var(--primary-blue);
    backdrop-filter: blur(10px);
}

.btn-outline:hover {
    background: var(--gradient-primary);
    color: var(--white);
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-xl);
    text-decoration: none;
}

.btn-success {
    background: var(--gradient-success);
    color: var(--white);
    border-color: var(--secondary-green);
}

.btn-success:hover {
    background: var(--gradient-secondary);
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-2xl);
    color: var(--white);
    text-decoration: none;
}

.btn-block {
    width: 100%;
}

/* Special Registration Button */
.nav-actions .btn-primary {
    padding: 1.3rem 2.8rem;
    font-size: 1.5rem;
    font-weight: 700;
    box-shadow: var(--shadow-lg);
    background: var(--gradient-orange);
    border-color: var(--primary-orange);
    border-radius: var(--radius-2xl);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.nav-actions .btn-primary:hover {
    background: var(--gradient-vibrant);
    transform: translateY(-1px) scale(1.02);
    box-shadow: var(--shadow-2xl);
}

/* ========================================
   Modern Header Styles
   ======================================== */

.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999; /* Reduce z-index to prevent interference with hero content */
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border-bottom: 1px solid rgba(26, 54, 93, 0.1);
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(25px);
}

/* Modern Top Bar */
.top-bar {
    background: var(--gradient-modern);
    color: var(--white);
    padding: 0.8rem 0;
    font-size: 1.1rem;
    position: relative;
    overflow: hidden;
}

.top-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmerTopBar 3s infinite;
}

@keyframes shimmerTopBar {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: -100%; }
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1.5rem;
    position: relative;
    z-index: 2;
}

.contact-info {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    opacity: 0.9;
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

.info-item i {
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.4rem;
    border-radius: var(--radius-full);
    transition: all var(--transition-fast);
}

/* Modern Clickable info items */
.info-link {
    color: inherit;
    text-decoration: none;
    transition: all var(--transition-normal);
    cursor: pointer;
    border-radius: var(--radius-md);
    padding: 0.3rem 0.6rem;
}

.info-link:hover {
    opacity: 1;
    transform: translateY(-2px);
    color: var(--white);
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
}

.info-link:hover i {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.social-links {
    display: flex;
    gap: 0.8rem;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    background: rgba(255, 255, 255, 0.15);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--radius-full);
    transition: all var(--transition-normal);
    backdrop-filter: blur(15px);
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-link i {
    font-size: 1.2rem;
    line-height: 1;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all var(--transition-fast);
}

.social-link:hover {
    background: var(--primary-orange);
    color: var(--white);
    transform: translateY(-3px) scale(1.1);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-orange);
}

.social-link:hover i {
    transform: translate(-50%, -50%) scale(1.2);
}

/* Main Navigation */
.main-nav {
    padding: 1rem 0;
    background: #FFFFFF;
    position: relative;
    border-bottom: 1px solid var(--gray-200);
}

.nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    position: relative;
    max-width: 100%;
    overflow: visible;
}

/* Logo Section */
.logo-section {
    flex-shrink: 0;
    min-width: max-content;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    text-decoration: none;
    color: var(--text-primary);
}

.logo-image {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.main-logo {
    height: 6rem;
    width: auto;
    max-width: 9rem;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.mobile-logo {
    height: 5rem;
    width: auto;
    max-width: 7rem;
    object-fit: contain;
}

/* Logo Circle - Deprecated, keeping for backward compatibility */
.logo-circle {
    width: 5rem;
    height: 5rem;
    background: var(--gradient-secondary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2rem;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.logo-circle::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.logo-text {
    display: flex;
    flex-direction: column;
}

/* Main conference title in header - REMOVE MULTICOLOR */
.conference-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-blue);
    line-height: 1.2;
    margin: 0;
}

.conference-year {
    font-size: 1.6rem;
    font-weight: 500;
    color: var(--primary-orange);
    margin-top: -0.2rem;
}

/* Navigation Menu */
.nav-menu {
    flex: 1;
    display: flex;
    justify-content: center;
    overflow: visible; /* Changed to visible for dropdowns */
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding: 0 1rem;
    margin: 0 1rem;
    position: static;
    z-index: 100;
}

.nav-menu::-webkit-scrollbar {
    display: none;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 0.3rem;
    align-items: center;
    white-space: nowrap;
    min-width: max-content;
    margin: 0;
    padding: 0;
    position: relative;
    overflow: visible; /* Ensure dropdowns are visible */
    z-index: 101;
}

.nav-item {
    position: relative;
    flex-shrink: 0;
    margin: 0;
    padding: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.2rem;
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    position: relative;
    white-space: nowrap;
    min-width: max-content;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-blue-dark);
    background: linear-gradient(135deg, var(--gray-50) 0%, rgba(255, 140, 66, 0.05) 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(46, 89, 144, 0.1);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 2rem;
    height: 3px;
    background: var(--gradient-orange);
    border-radius: 2px;
}

.nav-link i {
    font-size: 1rem;
    transition: transform var(--transition-fast);
}

/* Dropdown Arrow Animation */
.nav-item.dropdown:hover .nav-link i {
    transform: rotate(180deg);
}

/* ===== IMPROVED DROPDOWN STYLES ===== */

/* Dropdown Menu Container */
.dropdown-menu {
    position: absolute;
    top: calc(100% + 2px);
    left: 0;
    min-width: 240px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    list-style: none;
    padding: 10px 0;
    margin: 0;
    z-index: 999999;

    /* Initially hidden */
    opacity: 0;
    visibility: hidden;
    transform: translateY(-5px);
    transition: all 0.2s ease;
    pointer-events: none;
}

/* Enhanced hover zone to prevent menu closing - Only for direct dropdown hover */
.nav-item.dropdown:hover::before {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 10px;
    background: transparent;
    z-index: 999998;
    pointer-events: auto;
}

/* Show dropdown ONLY on direct hover of the dropdown nav item */
.nav-item.dropdown:hover > .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    pointer-events: auto;
}

/* Keep dropdown visible when hovering directly over it */
.nav-item.dropdown .dropdown-menu:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    pointer-events: auto;
}

/* Enhanced dropdown behavior - More specific selectors */
.nav-item.dropdown {
    position: relative;
}

/* Remove broad selectors that might cause issues */
.nav-item.dropdown:hover > .dropdown-menu,
.nav-item.dropdown .dropdown-menu:hover {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    pointer-events: auto;
}

/* Prevent menu from closing on quick mouse movements - Only within dropdown */
.nav-item.dropdown:hover::before,
.nav-item.dropdown:hover > .dropdown-menu {
    transition-delay: 0s;
}

/* Default state with slight delay */
.nav-item.dropdown .dropdown-menu {
    transition-delay: 0.1s;
}

/* Hide state with delay */
.nav-item.dropdown:not(:hover) .dropdown-menu {
    transition-delay: 0.15s;
}

/* Dropdown Menu Items */
.dropdown-menu li {
    margin: 0;
    padding: 0;
}

.dropdown-menu a {
    display: block;
    padding: 14px 22px;
    color: #333;
    text-decoration: none;
    font-size: 1.3rem;
    font-weight: 500;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.dropdown-menu a:hover {
    background-color: #f8f9fa;
    color: #0b4c7a;
    border-left-color: #2e7d32;
    padding-left: 28px;
    transform: translateX(2px);
}

/* Action Buttons */
.nav-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-shrink: 0;
    margin-left: 1rem;
    position: relative;
    z-index: 3;
}

/* Mobile Toggle */
.mobile-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 3rem;
    height: 2.4rem;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
}

.hamburger {
    width: 100%;
    height: 3px;
    background: var(--primary-blue);
    border-radius: 2px;
    transition: all var(--transition-fast);
}

.mobile-toggle.active .hamburger:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-toggle.active .hamburger:nth-child(2) {
    opacity: 0;
}

.mobile-toggle.active .hamburger:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* ========================================
   Mobile Menu Styles
   ======================================== */

.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    z-index: 999999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    backdrop-filter: blur(5px);
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-menu {
    position: fixed;
    top: 0;
    right: 0;
    width: 32rem;
    max-width: 90vw;
    height: 100vh;
    background: var(--white);
    padding: 2rem;
    transform: translateX(100%);
    transition: transform var(--transition-normal);
    overflow-y: auto;
    box-shadow: 0 0 30px rgba(11, 76, 122, 0.15);
}

.mobile-menu-overlay.active .mobile-menu {
    transform: translateX(0);
}

.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(11, 76, 122, 0.1);
}

.mobile-menu-header .logo-text h2 {
    color: var(--primary-blue);
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
}

.mobile-menu-header .mobile-logo {
    height: 4.5rem !important;
    width: auto !important;
    max-width: 6.5rem !important;
    object-fit: contain !important;
}

.mobile-menu-header .logo-container {
    gap: 1.5rem;
}

.mobile-close {
    width: 3rem;
    height: 3rem;
    background: transparent;
    border: none;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--primary-blue);
    font-size: 1.5rem;
    transition: all var(--transition-fast);
}

.mobile-close:hover {
    background: rgba(11, 76, 122, 0.1);
    transform: rotate(90deg);
}

.mobile-nav-list {
    list-style: none;
    margin-bottom: 3rem;
}

.mobile-nav-list > li {
    margin-bottom: 0.5rem;
}

.mobile-nav-link {
    display: block;
    padding: 1.5rem 1rem;
    color: var(--primary-blue);
    text-decoration: none;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    border: none;
    position: relative;
    margin-bottom: 0.3rem;
}

.mobile-nav-link:hover,
.mobile-nav-link:focus {
    background: rgba(26, 54, 93, 0.1);
    color: var(--primary-blue-dark);
    transform: translateX(0.5rem);
    box-shadow: var(--shadow-md);
}

/* Special Registration Button in Mobile Menu */
.mobile-nav-list li:last-child .mobile-nav-link {
    background: var(--gradient-orange);
    color: var(--white);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-xl);
    margin-top: 1rem;
    position: relative;
    overflow: hidden;
}

.mobile-nav-list li:last-child .mobile-nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.mobile-nav-list li:last-child .mobile-nav-link:hover::before {
    left: 100%;
}

.mobile-nav-list li:last-child .mobile-nav-link:hover,
.mobile-nav-list li:last-child .mobile-nav-link:focus {
    background: var(--gradient-vibrant);
    color: var(--white);
    transform: translateX(0) scale(1.05);
    box-shadow: var(--shadow-2xl);
}

/* Mobile dropdown styles */
.mobile-dropdown-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-arrow {
    font-size: 1rem;
    transition: transform var(--transition-fast);
}

.mobile-dropdown.active .mobile-arrow {
    transform: rotate(180deg);
}

.mobile-dropdown.active .mobile-submenu {
    display: block;
}

.mobile-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Dropdown positioning */
.nav-item.dropdown {
    position: relative !important;
}

/* Right align for last dropdown items */
.nav-item.dropdown:last-child .dropdown-menu,
.nav-item.dropdown:nth-last-child(2) .dropdown-menu {
    left: auto !important;
    right: 0 !important;
}

/* Remove active menu underline */
.nav-link.active::after {
    display: none !important;
}

/* Registration Button in Navigation */
.nav-actions .btn {
    font-size: 1.3rem;
    padding: 0.8rem 1.6rem;
    font-weight: 600;
    white-space: nowrap;
}

.nav-actions .btn i {
    font-size: 1.1rem;
}

/* ========================================
   Quick Info Section
   ======================================== */

.quick-info {
    padding: 6rem 0;
    background: var(--gray-50);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
    gap: 3rem;
    margin-top: 2rem;
}

.info-item {
    text-align: center;
    padding: 1.2rem 1.1rem;
    cursor: pointer;
    font-size: 13px;
    /* background: var(--white); */
    /* border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal); */
    /* border-top: 4px solid var(--primary-blue); */
}

/* .info-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-top-color: var(--secondary-green);
} */

.info-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 6rem;
    height: 6rem;
    background: var(--gradient-orange);
    color: var(--white);
    border-radius: var(--radius-full);
    margin-bottom: 2rem;
    font-size: 2.4rem;
}

.info-item h3 {
    font-size: 2rem;
    font-weight: var(--font-weight-semibold);
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.info-item p {
    font-size: 1.4rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* ========================================
   About Section Styles - Modern Indian Design
   ======================================== */

.about-section {
    padding: 2rem 0; /* Reduced from 8rem */
    background: linear-gradient(135deg, #f7fafc 0%, #ffffff 50%, #f7fafc 100%);
    position: relative;
    overflow: hidden;
    margin: 0;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 20% 80%, rgba(255, 140, 66, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(46, 89, 144, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(76, 175, 80, 0.05) 0%, transparent 50%);
    z-index: 0;
}

/* Section Header */
.section-header {
    text-align: center;
    margin-bottom: 3rem; /* Reduced from 6rem to 3rem */
    padding: 0 1.5rem; /* Reduced side padding */
    position: relative;
    z-index: 1;
}

.section-title-wrapper {
    margin-bottom: 1.5rem; /* Reduced from 2rem */
}

/* Section subtitle */
.section-subtitle {
    display: inline-block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-orange);
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 1rem; /* Reduced from 1.5rem */
    position: relative;
}

.section-subtitle::after {
    /* content: ''; */
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 3rem;
    height: 2px;
    background: var(--gradient-orange);
    border-radius: 1px;
}

/* Panel titles - REMOVE MULTICOLOR */
.section-title {
    font-size: 4.5rem;
    font-weight: 800;
    color: var(--primary-blue-dark); /* Restore original color */
    background: linear-gradient(135deg, #1A3A5C 0%, #FF8C42 25%, #2E5990 50%, #4CAF50 75%, #E6652A 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 400% 400%;
    animation: gradientShift 4s ease-in-out infinite;
    margin-bottom: 1.5rem; /* Reduced from 2rem */
    line-height: 1.2;
    position: relative;
    text-align: center;
}

.title-decoration {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin: 1.5rem 0; /* Reduced from 2.5rem */
}

.decoration-line {
    width: 5rem;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: 1px;
}

.decoration-symbol {
    font-size: 2rem;
    color: var(--primary-orange);
    font-weight: bold;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 1; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 0.8; }
}

/* Section description */
.section-description {
    font-size: var(--font-size-md);
    color: var(--gray-600);
    max-width: 60rem;
    margin: 0 auto 2rem auto; /* Reduced from 4rem to 2rem */
    line-height: 1.6;
}

/* About Grid Layout - 3 Column Layout for Desktop */
.about-grid {
    display: grid;
    grid-template-columns: 300px 1fr 300px;
    gap: 3rem;
    align-items: start;
    position: relative;
    z-index: 1;
    margin-top: 1rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

/* Side panels positioning for 3-column layout */
.quick-links-panel,
.contact-info-panel {
    position: relative;
}

/* Panel Headers */
.panel-header {
    margin-bottom: 2rem; /* Reduced from 3rem */
    text-align: center;
}

/* Panel titles - REMOVE MULTICOLOR */
.panel-title {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-blue-dark); /* Restore original color */
    margin-bottom: 1rem; /* Reduced from 1.5rem */
}

.panel-title i {
    color: var(--primary-orange);
    font-size: 2.2rem;
}

.panel-decoration {
    width: 6rem;
    height: 3px;
    background: var(--gradient-orange);
    margin: 0 auto;
    border-radius: 2px;
    position: relative;
}

.panel-decoration::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1rem;
    height: 1rem;
    background: var(--primary-orange);
    border-radius: 50%;
    border: 3px solid var(--white);
}

/* Left Panel - Quick Links */
.quick-links-panel {
    background: #ffffff;
    border-radius: var(--radius-2xl);
    padding: 3rem 2rem;
    box-shadow: 0 10px 15px -3px rgba(46, 89, 144, 0.1), 0 4px 6px -2px rgba(46, 89, 144, 0.05);
    border: 1px solid #e2e8f0;
    position: sticky;
    top: 12rem;
    transition: all var(--transition-normal);
}

.quick-links-panel:hover {
    transform: translateY(-0.5rem);
    box-shadow: 0 25px 50px -12px rgba(46, 89, 144, 0.25);
}

.quick-links-list {
    list-style: none;
}

.quick-link-item {
    margin-bottom: 1rem;
}

.quick-link {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1.5rem;
    background: #f7fafc;
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: all var(--transition-normal);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.quick-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left var(--transition-slow);
    z-index: 0;
}

.quick-link:hover::before {
    left: 0;
}

.quick-link:hover {
    border-color: var(--primary-blue);
    transform: translateX(0.5rem);
    box-shadow: var(--shadow-md);
}

.quick-link:hover .link-icon i,
.quick-link:hover .link-title,
.quick-link:hover .link-subtitle,
.quick-link:hover .link-arrow i {
    color: #ffffff !important;
    position: relative;
    z-index: 1;
}

.link-icon {
    flex-shrink: 0;
    width: 4rem;
    height: 4rem;
    background: var(--gradient-orange);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
}

.link-icon i {
    font-size: 1.8rem;
    color: var(--white);
}

.link-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

/* Link titles in quick links - REMOVE MULTICOLOR */
.link-title {
    display: block;
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--gray-800); /* Restore original color */
    margin-bottom: 0.5rem;
    transition: color var(--transition-normal); /* Restore color transition */
}

.link-subtitle {
    display: block;
    font-size: 1.2rem;
    color: var(--gray-500);
    transition: color var(--transition-normal);
}

.link-arrow {
    flex-shrink: 0;
    position: relative;
    z-index: 1;
}

.link-arrow i {
    font-size: 1.4rem;
    color: var(--gray-400);
    transition: all var(--transition-normal);
}

/* Center Panel - Event Message - Full Width */
.event-message-panel {
    background: #ffffff;
    border-radius: var(--radius-2xl);
    padding: 2rem 2rem;
    box-shadow: 0 20px 25px -5px rgba(46, 89, 144, 0.1), 0 10px 10px -5px rgba(46, 89, 144, 0.04);
    border: 3px solid #ff8c42;
    position: relative;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 140, 66, 0.1) 0%, transparent 25%),
        radial-gradient(circle at 75% 75%, rgba(46, 89, 144, 0.1) 0%, transparent 25%);
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.event-message-panel::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #2e5990 0%, #ff8c42 50%, #4caf50 100%);
    border-radius: var(--radius-2xl);
    z-index: -1;
}

.message-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid var(--gray-100);
}

.header-logo {
    flex-shrink: 0;
}

.message-logo {
    width: 8rem;
    height: 8rem;
    object-fit: contain;
    filter: drop-shadow(0 4px 8px rgba(46, 89, 144, 0.2));
}

.header-text {
    flex: 1;
}

/* Message title - REMOVE MULTICOLOR */
.message-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-orange);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Message main title - REMOVE MULTICOLOR */
.message-main-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-extrabold);
    color: var(--primary-blue-dark);
    margin-bottom: 0.5rem;
    line-height: 1.1;
}

.message-date {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-green);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hosted-by {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: var(--gradient-orange);
    border-radius: var(--radius-lg);
    color: var(--white);
}

/* Hosted title - REMOVE MULTICOLOR */
.hosted-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--white); /* Keep white since it's on colored background */
    margin-bottom: 1rem;
    letter-spacing: 2px;
}

.hosted-text {
    font-size: 1.6rem;
    font-weight: 600;
    line-height: 1.4;
}

.message-content {
    line-height: 1.7;
}

.greeting-text {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--primary-blue-dark);
    margin-bottom: 1rem;
}

.seasons-text {
    font-size: 1.6rem;
    font-weight: 500;
    color: var(--primary-green);
    margin-bottom: 2rem;
}

.message-paragraph {
    font-size: var(--font-size-base);
    color: var(--gray-700);
    margin-bottom: 2rem;
    text-align: justify;
    line-height: 1.7;
}

.message-paragraph.highlight {
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--primary-blue-dark);
    text-align: center;
    padding: 2rem;
    background: var(--gradient-secondary);
    color: var(--white);
    border-radius: var(--radius-lg);
    margin: 3rem 0;
}

.organizing-team {
    margin-top: 3rem;
    text-align: center;
    padding: 2rem;
    background: #f7fafc;
    border-radius: var(--radius-lg);
    border-left: 4px solid #ff8c42;
}

.team-greeting {
    font-size: 1.4rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

/* Team title - REMOVE MULTICOLOR */
.team-title {
    font-size: 1.6rem;
    font-weight: 700;
    color: var(--primary-blue-dark); /* Restore original color */
    margin-bottom: 0.3rem;
}

.team-event {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--primary-orange);
    text-transform: uppercase;
}

.conference-seal {
    display: flex;
    justify-content: center;
    margin-top: 0;
    flex-shrink: 0;
}

.seal-container {
    position: relative;
    width: 8rem;
    height: 8rem;
}

.conference-seal-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 4px 12px rgba(46, 89, 144, 0.3));
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.seal-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;
}

.seal-year {
    display: block;
    font-size: 1.8rem;
    font-weight: 800;
    color: var(--primary-blue-dark);
    line-height: 1;
}

.seal-event {
    font-size: 1rem;
    font-weight: 600;
    color: var(--primary-orange);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Right Panel - Contact Information */
.contact-info-panel {
    background: #ffffff;
    border-radius: var(--radius-2xl);
    padding: 3rem 2rem;
    box-shadow: 0 10px 15px -3px rgba(46, 89, 144, 0.1), 0 4px 6px -2px rgba(46, 89, 144, 0.05);
    border: 1px solid #e2e8f0;
    position: sticky;
    top: 12rem;
    transition: all var(--transition-normal);
}

.contact-info-panel:hover {
    transform: translateY(-0.5rem);
    box-shadow: 0 25px 50px -12px rgba(46, 89, 144, 0.25);
}

.secretariat-carousel {
    margin-bottom: 3rem;
}

.carousel-container {
    position: relative;
    background: #f7fafc;
    border-radius: var(--radius-xl);
    padding: 1rem;
    border: 2px solid #e2e8f0;
}

.secretariat-slides {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-lg);
    min-height: 20rem
}

.secretariat-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transform: translateY(2rem);
    transition: all var(--transition-slow);
}

.secretariat-slide.active {
    opacity: 1;
    transform: translateY(0);
}

.secretary-card {
    background: #ffffff;
    padding: 2.5rem 2rem;
    border-radius: var(--radius-lg);
    box-shadow: 0 4px 6px -1px rgba(46, 89, 144, 0.1), 0 2px 4px -1px rgba(46, 89, 144, 0.06);
    text-align: center;
    border: 2px solid #2e5990;
    min-height: 18rem;
}

.secretary-avatar {
    width: 6rem;
    height: 6rem;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    box-shadow: var(--shadow-lg);
}

.secretary-avatar i {
    font-size: 2.5rem;
    color: var(--white);
}

/* Secretary names - REMOVE MULTICOLOR */
.secretary-name {
    font-size: 2rem;
    font-weight: 700;
    color: #1a3a5c; /* Restore original color */
    margin-bottom: 0.8rem;
}

/* Secretary titles - REMOVE MULTICOLOR */
.secretary-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #ff8c42; /* Restore original orange color */
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.secretary-contact {
    display: none;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    padding: 1.3rem 1.5rem;
    background: #f7fafc;
    border-radius: var(--radius-md);
    transition: background var(--transition-normal);
    margin-bottom: 0.8rem;
}

.contact-item:hover {
    background: #2e5990;
    color: #ffffff;
}

.contact-item i {
    font-size: 1.4rem;
    color: var(--primary-orange);
    width: 2rem;
    text-align: center;
}

.contact-item:hover i {
    color: var(--white);
}

.contact-item span {
    font-size: 1.4rem;
    font-weight: 500;
    line-height: 1.2;
}

.carousel-navigation {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.carousel-btn {
    width: 4rem;
    height: 4rem;
    background: var(--gradient-primary);
    border: none;
    border-radius: var(--radius-full);
    color: var(--white);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.carousel-btn:hover {
    transform: translateY(-0.2rem);
    box-shadow: var(--shadow-lg);
}

.carousel-btn i {
    font-size: 1.4rem;
}

/* Quick Contact Actions */
.quick-contact-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 1.2rem 2rem;
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 1.4rem;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.contact-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.contact-action-btn:hover::before {
    left: 100%;
}

.call-btn {
    background: var(--gradient-secondary);
    color: var(--white);
    border: 2px solid var(--primary-green);
}

.call-btn:hover {
    transform: translateY(-0.2rem);
    box-shadow: var(--shadow-lg);
}

.email-btn {
    background: var(--gradient-orange);
    color: var(--white);
    border: 2px solid var(--primary-orange);
}

.email-btn:hover {
    transform: translateY(-0.2rem);
    box-shadow: var(--shadow-lg);
}

.register-btn {
    background: var(--gradient-primary);
    color: var(--white);
    border: 2px solid var(--primary-blue);
}

.register-btn:hover {
    transform: translateY(-0.2rem);
    box-shadow: var(--shadow-lg);
}

/* ========================================
   About Section Responsive Design
   ======================================== */

/* Large Tablets and Small Desktops */
@media (max-width: 1200px) {
    .about-grid {
        grid-template-columns: 280px 1fr 280px;
        gap: 2.5rem;
        max-width: 1000px;
    }

    .section-title {
        font-size: 4rem;
    }

    .section-title::after,
    .message-main-title::after {
        right: -2.5rem;
        font-size: 1.8rem;
    }

    .event-message-panel {
        padding: 3rem 2.5rem;
    }

    .quick-links-panel,
    .contact-info-panel {
        padding: 2.5rem 2rem;
    }
}

/* Medium Tablets - Switch to column layout for 1024x1366 */
@media (max-width: 1024px) and (min-width: 769px) {
    .about-grid {
        display: flex;
        flex-direction: column;
        gap: 3rem;
        max-width: 900px;
    }

    .bottom-panels-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2.5rem;
        width: 100%;
    }

    .event-message-panel {
        max-width: 900px;
        margin: 0 auto;
    }

    .quick-links-panel,
    .contact-info-panel {
        position: static;
    }

    .section-title {
        font-size: 3.5rem;
    }

    .about-section {
        padding: 6rem 0;
    }
}

/* Small Tablets - Stack panels vertically */
@media (max-width: 768px) {
    .about-section {
        padding: 4rem 0;
    }

    .section-header {
        margin-bottom: 3rem;
    }

    .section-title {
        font-size: 2.8rem;
    }

    .about-grid {
        display: flex;
        flex-direction: column;
        gap: 3rem;
    }

    .bottom-panels-row {
        display: flex;
        flex-direction: column;
        gap: 3rem;
        max-width: 100%;
    }

    .event-message-panel {
        max-width: 100%;
    }

    .message-header {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .message-logo {
        width: 8rem;
        height: 8rem;
    }

    .message-main-title {
        font-size: 2.5rem;
    }

    .section-title::after,
    .message-main-title::after {
        right: -1.8rem;
        font-size: 1.4rem;
    }

    .quick-links-panel,
    .contact-info-panel {
        padding: 2.5rem 2rem;
        max-width: 60rem;
        margin: 0 auto;
    }

    .quick-contact-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    /* New responsive styles for organizing team section */
    .organizing-team-section {
        flex-direction: column;
        align-items: center;
        gap: 1.5rem;
        text-align: center;
    }
    
    .organizing-team {
        text-align: center;
    }
}

/* Mobile Devices */
@media (max-width: 480px) {
    .about-section {
        padding: 3rem 0;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .section-description {
        font-size: 1.6rem;
    }

    .about-grid {
        gap: 2rem;
    }

    .bottom-panels-row {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        max-width: 100%;
    }

    .event-message-panel {
        padding: 2rem 1.5rem;
        border-width: 2px;
        max-width: 100%;
    }

    .message-main-title {
        font-size: 2.2rem;
    }

    .section-title::after,
    .message-main-title::after {
        right: -1.5rem;
        font-size: 1.3rem;
    }

    .quick-links-panel,
    .contact-info-panel {
        padding: 2rem 1.5rem;
    }

    .quick-link {
        padding: 1.2rem;
        gap: 1rem;
    }

    .link-icon {
        width: 3.5rem;
        height: 3.5rem;
    }

    .link-icon i {
        font-size: 1.6rem;
    }

    .link-title {
        font-size: 1.4rem;
    }

    .link-subtitle {
        font-size: 1.1rem;
    }

    .quick-contact-actions {
        flex-direction: column;
    }

    .contact-action-btn {
        font-size: 1.3rem;
    }
    
    .secretary-card {
        padding: 2rem 1.5rem;
    }
    
    /* Responsive styles for organizing team section on small screens */
    .organizing-team-section {
        padding: 1.5rem;
        gap: 1rem;
    }
    
    .seal-container {
        width: 6rem;
        height: 6rem;
    }
}

/* Extra Small Devices */
@media (max-width: 360px) {
    .section-title {
        font-size: 2.2rem;
    }
    
    .conference-title {
        font-size: 1.4rem;
    }
    
    .section-title::after,
    .message-main-title::after {
        right: -1rem;
        font-size: 1.2rem;
    }
    
    .event-message-panel {
        padding: 1.5rem 1rem;
    }
    
    .quick-links-panel,
    .contact-info-panel {
        padding: 1.5rem 1rem;
    }
    
    .decoration-line {
        width: 3rem;
    }
    
    .title-decoration {
        gap: 1rem;
    }
    
    /* Extra small screen styles for organizing team section */
    .organizing-team-section {
        padding: 1rem;
        gap: 1rem;
    }
    
    .seal-container {
        width: 5rem;
        height: 5rem;
    }
    
    .team-greeting,
    .team-title,
    .team-event {
        font-size: 1.2rem;
    }
}

/* ========================================
   Responsive Design
   ======================================== */

@media (max-width: 1200px) {
    .nav-link {
        padding: 1rem 1rem;
        font-size: 1.2rem;
    }

    .nav-list {
        gap: 0.2rem;
    }

    .container {
        padding: 0 1.5rem;
    }
}

@media (max-width: 1150px) {
    .nav-link {
        padding: 0.9rem 0.8rem;
        font-size: 1.15rem;
    }

    .nav-list {
        gap: 0.15rem;
    }

    .nav-actions .btn {
        padding: 0.7rem 1.4rem;
        font-size: 1.05rem;
    }
}

@media (max-width: 1024px) {
    .nav-actions .btn {
        padding: 0.6rem 1rem;
        font-size: 1rem;
    }

    .nav-link {
        padding: 0.8rem 0.6rem;
        font-size: 1.05rem;
    }

    .nav-list {
        gap: 0.05rem;
    }

    .nav-menu {
        margin: 0 0.4rem;
    }

    .nav-actions {
        margin-left: 0.4rem;
    }
}

@media (max-width: 1100px) {
    .nav-link {
        padding: 0.8rem 0.7rem;
        font-size: 1.1rem;
    }

    .nav-list {
        gap: 0.1rem;
    }

    .nav-actions .btn {
        padding: 0.6rem 1.1rem;
        font-size: 1rem;
    }
}

@media (max-width: 850px) {
    .nav-menu,
    .nav-actions {
        display: none;
    }

    .mobile-toggle {
        display: flex;
    }
}

@media (min-width: 851px) and (max-width: 950px) {
    .nav-link {
        padding: 0.7rem 0.5rem;
        font-size: 1rem;
    }

    .nav-list {
        gap: 0.02rem;
    }

    .nav-actions .btn {
        padding: 0.6rem 0.9rem;
        font-size: 0.95rem;
    }

    .nav-menu {
        margin: 0 0.2rem;
        padding: 0 0.3rem;
    }

    .nav-actions {
        margin-left: 0.3rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 1.5rem;
    }

    .top-bar {
        padding: 0.4rem 0;
        font-size: 1rem;
    }

    .top-bar-content {
        justify-content: center;
        gap: 0;
    }

    .contact-info {
        justify-content: center;
        gap: 1rem;
    }

    .info-item {
        font-size: 1.3rem;
    }

    /* Hide calendar date and social links on mobile */
    .info-item:nth-child(3),
    .social-links {
        display: none;
    }

    .main-nav {
        padding: 1rem 0;
    }

    .nav-menu,
    .nav-actions {
        display: none;
    }

    .mobile-toggle {
        display: flex;
    }

    .main-logo {
        height: 4.5rem;
        max-width: 6rem;
    }

    .mobile-logo {
        height: 2.5rem;
        max-width: 4rem;
    }

    .conference-title {
        font-size: 2rem;
    }

    .conference-year {
        font-size: 1.4rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .top-bar {
        padding: 0.3rem 0;
    }

    .contact-info {
        gap: 0.8rem;
    }

    .info-item {
        font-size: 1.2rem;
    }

    .logo-container {
        gap: 1rem;
    }

    .main-logo {
        height: 4rem;
        max-width: 5.5rem;
    }

    .mobile-logo {
        height: 2rem;
        max-width: 3rem;
    }

    .conference-title {
        font-size: 1.6rem;
    }

    .conference-year {
        font-size: 1.1rem;
    }

    .mobile-menu {
        width: 100%;
        max-width: 100vw;
        padding: 1.5rem;
    }

    .mobile-nav-link {
        padding: 1.2rem 0.8rem;
        font-size: 1.5rem;
    }

    .top-bar-content {
        padding: 0.5rem 0;
    }
}

@media (max-width: 360px) {
    .container {
        padding: 0 0.8rem;
    }

    .conference-title {
        font-size: 1.4rem;
    }

    .conference-year {
        font-size: 1rem;
    }

    .main-logo {
        height: 3.5rem;
        max-width: 4.5rem;
    }    .mobile-logo {
        height: 1.8rem;
        max-width: 2.5rem;
    }
    
    .slider-indicators {
        bottom: 15vh; /* Further adjust for very small screens */
    }

    .mobile-nav-link {
        padding: 1rem 0.5rem;
        font-size: 1.4rem;
    }

    .info-item {
        font-size: 0.8rem;
    }

    .contact-info {
        gap: 0.6rem;
    }
}

/* ========================================
   Large Screen Optimizations
   ======================================== */

@media (min-width: 1400px) {
    .nav-link {
        padding: 1.2rem 1.8rem;
        font-size: 1.4rem;
    }

    .nav-list {
        gap: 0.5rem;
    }

    .container {
        padding: 0 3rem;
    }

    .main-logo {
        height: 6.5rem;
        max-width: 10rem;
    }
}

@media (min-width: 1600px) {
    .container {
        padding: 0 4rem;
    }

    .nav-link {
        padding: 1.2rem 2rem;
        font-size: 1.5rem;
    }

    .nav-list {
        gap: 0.6rem;
    }

    .main-logo {
        height: 7rem;
        max-width: 11rem;
    }
}

/* ========================================
   Navigation Scroll Handling
   ======================================== */

.nav-content {
    position: relative;
}

/* Removed fade indicators to prevent white fading issues */

/* ========================================
   Performance Optimizations
   ======================================== */

/* Improve rendering performance */
.header,
.main-nav,
.dropdown-menu,
.mobile-menu-overlay {
    will-change: transform;
}

/* Reduce layout shift */
.logo-circle {
    contain: layout style paint;
}

/* Optimize animations */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* ========================================
   Accessibility Improvements
   ======================================== */

/* Focus styles */
.btn:focus,
.nav-link:focus,
.social-link:focus,
.mobile-toggle:focus {
    outline: none;
    box-shadow: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
        --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
    }
}

/* Dark mode support (basic) - Disabled for clean white theme */
@media (prefers-color-scheme: dark) {
    /* Keep everything white and clean - no dark mode */
    .header {
        background: var(--white);
        border-bottom: 1px solid var(--gray-200);
    }

    .main-nav {
        background: var(--white);
    }

    .dropdown-menu {
        background: var(--white);
        border-color: var(--gray-200);
    }

    .mobile-menu {
        background: var(--white);
    }

    .mobile-menu-overlay {
        background: rgba(255, 255, 255, 0.95);
    }
}

/* ========================================
   Page Content Spacing
   ======================================== */

/* Remove top padding to fix banner cutting issue */
body {
    padding-top: 0; /* Fixed: No more banner cutting */
}

/* Remove top padding on mobile for better spacing */
@media (max-width: 768px) {
    body {
        padding-top: 0; /* Ensure mobile banner displays properly */
    }
}

@media (max-width: 480px) {
    body {
        padding-top: 0; /* Ensure mobile banner displays properly */
    }
}

.mobile-nav-list li:last-child .mobile-nav-link:hover,
.mobile-nav-list li:last-child .mobile-nav-link:focus {
    background: var(--gradient-vibrant);
    color: var(--white);
    transform: translateX(0) scale(1.05);
    box-shadow: var(--shadow-2xl);
}

.mobile-submenu {
    list-style: none;
    margin-left: 2rem;
    margin-top: 0.5rem;
    display: none;
    transition: all var(--transition-fast);
}

.mobile-submenu li {
    margin-bottom: 0.5rem;
}

.mobile-submenu a {
    display: block;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    border-left: 3px solid #2e7d32;
    transition: all 0.2s ease;
    padding-left: 25px;
    border-radius: 4px;
}

.mobile-submenu a:hover,
.mobile-submenu a:focus,
.mobile-submenu a:active {
    background-color: #f8f9fa !important;
    color: #0b4c7a !important;
    border-left-color: #2e7d32 !important;
}

/* ========================================
   Multicolor Heading Styles - Only Main Title
   ======================================== */

/* ONLY Main section title gets multicolor - "National Conference of Excellence" */
.section-title {
    font-size: 4.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, #1A3A5C 0%, #FF8C42 25%, #2E5990 50%, #4CAF50 75%, #E6652A 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 400% 400%;
    animation: gradientShift 4s ease-in-out infinite;
    margin-bottom: 2rem; /* Consistent bottom margin */
    line-height: 1.2;
    position: relative;
    text-align: center;
}

/* Countdown title - REMOVE MULTICOLOR */
.countdown-title {
    color: white;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 25px;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    text-align: center;
}

/* Gradient animation keyframes - only for main title */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Additional sparkle effect for main title only */
.section-title::after {
    content: '✦';
    position: absolute;
    right: -3rem;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #FF8C42 0%, #4CAF50 50%, #2E5990 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-size: 2rem;
    /* Remove rotating animation */
    animation: none;
}

/* Remove sparkle keyframes animation that was causing rotation */
@keyframes sparkle {
    0%, 100% { 
        transform: translateY(-50%) scale(1); 
        opacity: 1;
    }
    50% { 
        transform: translateY(-50%) scale(1.3); 
        opacity: 0.7;
    }
}

/* Responsive adjustments for multicolor main title only */
@media (max-width: 1200px) {
    .section-title {
        font-size: 4rem;
    }
    
    .section-title::after {
        right: -2.5rem;
        font-size: 1.8rem;
    }
}

@media (max-width: 1024px) {
    .section-title {
        font-size: 3.5rem;
    }
}

@media (max-width: 768px) {
    .section-title {
        font-size: 3rem;
    }
    
    .section-title::after {
        right: -2rem;
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .section-title {
        font-size: 2.5rem;
    }
    
    .section-title::after {
        right: -1.5rem;
        font-size: 1.3rem;
    }
}

@media (max-width: 360px) {
    .section-title {
        font-size: 2.2rem;
    }
    
    .section-title::after {
        right: -1rem;
        font-size: 1.2rem;
    }
}

/* ========================================
   Header Countdown Timer Styles
   ======================================== */

.header-countdown {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 2rem;
    flex-shrink: 0;
}

.countdown-container-header {
    background: linear-gradient(135deg, #FF8C42 0%, #FF6B1A 50%, #E6652A 100%);
    padding: 0.8rem 1.5rem;
    border-radius: 1.2rem;
    box-shadow: 0 4px 15px rgba(255, 140, 66, 0.3);
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.countdown-container-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 140, 66, 0.4);
}

.countdown-title-header {
    color: white;
    font-size: 0.9rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.countdown-timer-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.time-unit-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.2rem;
}

.time-number-header {
    color: white;
    font-size: 1.6rem;
    font-weight: 900;
    line-height: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
    min-width: 2.5rem;
    font-family: 'Poppins', sans-serif;
    transition: transform 0.1s ease;
}

.time-label-header {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.6rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.time-separator-header {
    color: white;
    font-size: 1.2rem;
    font-weight: 900;
    margin: 0 0.2rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
    align-self: flex-start;
    margin-top: 0.2rem;
}


/* ========================================
   Top Bar Countdown Timer Styles
   ======================================== */



/* Mobile responsive for top bar with countdown */
@media (max-width: 768px) {
    .top-bar-content {
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .contact-info {
        order: 1;
        justify-content: center;
        gap: 1rem;
        flex: 1 0 100%;
    }
    
    
    .social-links {
        order: 3;
        justify-content: center;
        flex: 1 0 100%;
        margin-top: 0.5rem;
    }
}

@media (max-width: 480px) {
    .top-bar-content {
        padding: 0.3rem 0;
        gap: 0.5rem;
    }
    
    .contact-info {
        gap: 0.8rem;
    }
    
    .social-links {
        margin-top: 0.3rem;
    }
}

/* New horizontal layout for organizing team section */
.organizing-team-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    margin-top: 2rem;
    padding: 2rem;
    background: #f7fafc;
    border-radius: var(--radius-lg);
    border-left: 4px solid #ff8c42;
}

.organizing-team {
    flex: 1;
    text-align: left;
    margin-top: 0;
    padding: 0;
    background: transparent;
    border-radius: 0;
    border-left: none;
}

/* ========================================
   REGISTRATION SECTION STYLES
   ======================================== */

.registration-section {
    padding: 4rem 0; /* Reduced from 8rem */
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
    margin: 0; /* Remove any margin */
}

/* Light Pattern Background */
.registration-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(46, 89, 144, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(76, 175, 80, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 25% 75%, rgba(255, 140, 66, 0.02) 0%, transparent 50%),
        radial-gradient(circle at 75% 25%, rgba(46, 89, 144, 0.02) 0%, transparent 50%);
    background-size: 400px 400px, 350px 350px, 300px 300px, 250px 250px;
    background-position: 0 0, 50px 50px, 100px 100px, 150px 150px;
    opacity: 0.6;
    z-index: 1;
}

/* Subtle geometric pattern overlay */
.registration-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(45deg, transparent 40%, rgba(46, 89, 144, 0.01) 40%, rgba(46, 89, 144, 0.01) 60%, transparent 60%),
        linear-gradient(-45deg, transparent 40%, rgba(76, 175, 80, 0.01) 40%, rgba(76, 175, 80, 0.01) 60%, transparent 60%);
    background-size: 60px 60px, 60px 60px;
    background-position: 0 0, 30px 30px;
    opacity: 0.4;
    z-index: 1;
}

/* Ensure content is above pattern */
.registration-container {
    position: relative;
    z-index: 2;
}

/* Enhanced floating animation for pattern */
@keyframes floatPattern {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

.registration-section::before {
    animation: floatPattern 20s ease-in-out infinite;
}

.registration-section::after {
    animation: floatPattern 25s ease-in-out infinite reverse;
}

/* Clean Registration Header with Decorative Elements */
.registration-header-clean {
    text-align: center;
    margin-bottom: 3rem; /* Reduced from 5rem */
    padding: 0 1.5rem; /* Reduced side padding */
    position: relative;
    z-index: 3;
}

/* Decorative background for header */
.registration-header-clean::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(46, 89, 144, 0.05) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
}

.registration-main-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: 1rem; /* Reduced from 1.5rem */
    letter-spacing: -0.5px;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(46, 89, 144, 0.1);
}

/* Add subtle text shadow for better visibility */
.registration-main-title {
    text-shadow: 0 2px 4px rgba(46, 89, 144, 0.1);
}

.registration-early-bird {
    font-size: var(--font-size-base);
    color: var(--primary-blue);
    font-weight: var(--font-weight-medium);
    margin: 0 0 1.5rem 0; /* Reduced from 2rem */
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
    z-index: 2;
}

/* Add decorative line under early bird text */
.registration-early-bird::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

/* Home Registration Cards */
.home-registration-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem; /* Reduced from 2.5rem */
    margin: 2.5rem 0; /* Reduced from 4rem */
    padding: 0 1.5rem; /* Reduced side padding */
    max-width: var(--container-xl);
    margin-left: auto;
    margin-right: auto;
}

/* Delegate Fees Table Styles - Legacy styles kept for compatibility 
   Now replaced with styles in registration-section.css */
/* 
.delegate-fees-container {
    margin: 2rem auto 4rem;
    max-width: 100%;
    overflow-x: auto;
}

.delegate-fees-table {
    width: 100%;
    border-collapse: collapse;
    box-shadow: var(--shadow-md);
    background-color: var(--white);
    border-radius: var(--radius-md);
    overflow: hidden;
    font-family: var(--font-family-primary);
}

.delegate-fees-table th,
.delegate-fees-table td {
    padding: 0.75rem 1rem;
    text-align: center;
    border: 1px solid #e0e0e0;
}

.fee-category-header {
    background-color: var(--primary-blue-dark);
    color: var(--white);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.4;
}

.fee-period-header {
    background-color: var(--primary-red);
    color: var(--white);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}
*/

.fee-category {
    background-color: #f5f5f5;
    font-weight: var(--font-weight-semibold);
    text-align: left;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.fee-subcategory {
    text-align: left;
    padding-left: 1.5rem;
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    background-color: #f9f9f9;
    border-left: 3px solid var(--primary-blue);
}

.fee-amount {
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
}

.delegate-type {
    vertical-align: middle;
    color: var(--primary-blue-dark);
    font-weight: var(--font-weight-bold);
    font-size: var(--font-size-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background-color: #eef2f7;
    vertical-align: middle;
    color: var(--primary-blue-dark);
    font-size: var(--font-size-md);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.fee-amount {
    font-weight: var(--font-weight-bold);
    color: var(--primary-blue-dark);
    font-size: var(--font-size-md);
}

.fee-note {
    font-size: var(--font-size-xs);
    font-style: italic;
    color: var(--text-secondary);
    display: block;
    margin-top: 0.25rem;
}

/* Responsive styles for delegate fees table */
@media (max-width: 768px) {
    .delegate-fees-container {
        margin: 1.5rem auto 3rem;
    }
    
    .delegate-fees-table th,
    .delegate-fees-table td {
        padding: 0.5rem;
        font-size: 0.85rem;
    }
    
    .fee-category-header,
    .fee-period-header {
        font-size: 0.8rem;
    }
    
    .fee-amount {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .delegate-fees-container {
        margin: 1rem auto 2rem;
    }
    
    .delegate-fees-table {
        font-size: 0.75rem;
    }
    
    .fee-category-header,
    .fee-period-header {
        font-size: 0.7rem;
    }
    
    .fee-amount {
        font-size: 0.8rem;
    }
}

.register-now-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    background: var(--gradient-primary);
    color: var(--white);
    padding: 1rem 2.5rem;
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-md);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.register-now-btn:hover {
    background: var(--gradient-accent);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.view-all-registration {
    text-align: center;
    margin: 2rem 0 4rem;
}

/* Responsive styles for delegate pricing cards */
@media (max-width: 1200px) {
    .delegates-pricing-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .delegates-pricing-cards {
        grid-template-columns: 1fr;
    }
    
    .pricing-card-header {
        padding: 1.2rem;
    }
    
    .pricing-card-body {
        padding: 1.2rem;
    }
    
    .category-title {
        font-size: var(--font-size-xs);
    }
    
    .category-price {
        font-size: var(--font-size-sm);
    }
}

/* Registration Category Cards - Clean Design with IMA Colors */
.registration-category-card {
    background: var(--white);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-xl);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.registration-category-card::before {
    display: none;
}

.registration-category-card:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* Remove all featured styles */
.registration-category-card.featured {
    background: var(--white);
    border: 2px solid var(--border-color);
}

.registration-category-card.featured:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

/* Hide featured badge completely */
.featured-badge {
    display: none;
}

/* Card Header - Clean Style with IMA Colors */
.registration-category-card .card-header {
    padding: 2rem 1.5rem 1rem;
    text-align: center;
    background: var(--white);
    border-bottom: 1px solid var(--gray-100);
}

.registration-category-card .card-header::before,
.registration-category-card .card-header::after {
    display: none;
}

/* Theme-based card colors - Very subtle with IMA colors */
.isar-member .card-header {
    background: var(--white);
}

.non-isar-member .card-header {
    background: var(--white);
}

.foreign-delegate .card-header {
    background: var(--white);
}

/* Card Title - Clean Typography with IMA Colors */
.registration-category-card .card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.3;
}

/* Price Badge - Clean Design without heavy background */
.price-badge {
    display: inline-block;
    color: var(--primary-blue);
    padding: 0.5rem 0;
    border-radius: 0;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-extrabold);
    margin-bottom: 0.5rem;
    position: relative;
    overflow: hidden;
    background: transparent;
    border: none;
    text-shadow: none;
}

/* Alternative: Stylish border design */
.price-badge::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.registration-category-card:hover .price-badge {
    transform: none;
    color: var(--primary-blue-dark);
}

.registration-category-card:hover .price-badge::before {
    width: 80px;
}

.foreign-price {
    color: var(--primary-green);
}

.foreign-price::before {
    background: var(--gradient-secondary);
}

.early-bird-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-normal);
    margin: 0;
    padding: 0;
    background: transparent;
    border-radius: 0;
    display: block;
    border: none;
    margin-top: 0.5rem;
}

/* Card Body - Clean Layout */
.registration-category-card .card-body {
    padding: 1.5rem;
    background: var(--white);
}

.registration-category-card .card-body::before {
    display: none;
}

/* Benefits List - Clean with IMA Green Checkmarks */
.benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefits-list li {
    padding: 0.6rem 0;
    border-bottom: none;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-normal);
    position: relative;
    padding-left: 2rem;
    display: flex;
    align-items: center;
}

.benefits-list li:last-child {
    border-bottom: none;
}

.benefits-list li:hover {
    color: var(--text-secondary);
    background: transparent;
    border-radius: 0;
    padding-left: 2rem;
}

/* Show IMA green checkmark icons */
.benefits-list i {
    display: inline-block;
    color: var(--primary-green);
    font-size: 0.9rem;
    position: absolute;
    left: 0;
    top: 0.6rem;
    width: 1.2rem;
    height: 1.2rem;
    border-radius: var(--radius-full);
    background: var(--gray-50);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--primary-green-light);
}

.benefits-list li::before {
    display: none;
}

.benefits-list li:hover::before {
    display: none;
}

.benefits-list li:hover i {
    color: var(--primary-green);
}

/* Card Footer - Simple Button with IMA Colors */
.registration-category-card .card-footer {
    padding: 1.5rem;
    background: var(--white);
    text-align: center;
    border-top: 1px solid var(--gray-100);
}

.registration-category-card .register-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background: var(--gradient-primary);
    color: var(--white);
    padding: 0.8rem 2rem;
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    transition: all var(--transition-normal);
    border: none;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.registration-category-card .register-btn::before,
.registration-category-card .register-btn::after {
    display: none;
}

.registration-category-card .register-btn:hover {
    background: var(--gradient-accent);
    transform: none;
    box-shadow: var(--shadow-md);
}

.registration-category-card .register-btn i {
    font-size: 0.85rem;
    transition: none;
}

.registration-category-card .register-btn:hover i {
    transform: none;
}

/* All cards same button color with IMA theme */
.isar-member .register-btn,
.non-isar-member .register-btn,
.foreign-delegate .register-btn {
    background: var(--gradient-primary);
}

.isar-member .register-btn:hover,
.non-isar-member .register-btn:hover,
.foreign-delegate .register-btn:hover {
    background: var(--gradient-accent);
    box-shadow: var(--shadow-md);
}

/* View All Registration - Clean Style with IMA Colors */
.view-all-registration {
        text-align: center;
    margin: 2.5rem 0 1.5rem; /* Reduced margins */
    padding: 2rem 1.5rem; /* Reduced padding */
    background: var(--bg-secondary);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
        position: relative;
    overflow: hidden;
}

.view-all-registration::before,
.view-all-registration::after {
    display: none;
}

.view-all-text {
    font-size: var(--font-size-base);
        color: var(--text-secondary);
    margin-bottom: 1.5rem;
    font-weight: var(--font-weight-normal);
}

.view-all-btn {
    display: inline-flex;
        align-items: center;
        gap: 0.8rem;
    background: var(--gradient-primary);
    color: var(--white);
    padding: 1rem 2rem;
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
        transition: all var(--transition-normal);
    border: none;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.view-all-btn::before {
    display: none;
}

.view-all-btn:hover {
    background: var(--gradient-accent);
    transform: none;
    box-shadow: var(--shadow-md);
}

.view-all-btn i {
        font-size: 0.9rem;
    transition: none;
}

.view-all-btn:hover i {
    transform: translateX(3px);
}

/* ========================================
   VENUE SECTION STYLES
   ======================================== */

.venue-section {
    padding: 4rem 0; /* Reduced from 8rem */
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    position: relative;
    overflow: hidden;
    margin: 0; /* Remove any margin */
}

.venue-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 80%, rgba(46, 89, 144, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(76, 175, 80, 0.03) 0%, transparent 50%);
    pointer-events: none;
}

/* Venue Header */
.venue-header-center {
    text-align: center;
    margin-bottom: 3rem; /* Reduced from 6rem */
    position: relative;
    padding: 0 1.5rem; /* Reduced side padding */
}

.venue-main-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: 1rem; /* Reduced from 1.5rem */
    line-height: 1.2;
}

.venue-highlight {
    color: var(--primary-orange);
    position: relative;
}

.venue-underline {
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-orange));
    margin: 1rem auto; /* Reduced from 1.5rem */
    border-radius: 2px;
}

.venue-subtitle {
    font-size: var(--font-size-md);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    margin-top: 0.8rem; /* Reduced from 1rem */
}

/* Venue Grid Layout */
.venue-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2.5rem; /* Reduced from 4rem */
    align-items: stretch;
    max-width: 1400px;
    margin: 0 auto;
}

/* Left Side - Map Section */
.venue-map-section {
    position: relative;
    height: 100%; /* Ensure full height */
    display: flex; /* Add flex to control child height */
    flex-direction: column;
}

.map-container {
    background: white;
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 
        0 10px 25px -5px rgba(46, 89, 144, 0.1),
        0 4px 6px -2px rgba(46, 89, 144, 0.05);
    overflow: hidden;
    position: relative;
    flex: 1; /* Take full available height */
    display: flex;
    flex-direction: column;
}

.map-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-orange), var(--primary-green));
}

.map-container iframe {
    width: 100%;
    height: 100%; /* Changed from fixed 500px to 100% */
    min-height: 500px; /* Add minimum height */
    border: none;
    border-radius: 8px;
    flex: 1; /* Take remaining space */
}

/* Right Side - Venue Information Card */
.venue-info-section {
    position: relative;
    height: 100%; /* Ensure full height */
    display: flex; /* Add flex to control child height */
    flex-direction: column;
}

.venue-info-card {
    background: linear-gradient(135deg, #2E5990 0%, #1A3A5C 100%);
    color: white;
    padding: 3rem;
    border-radius: 16px;
    box-shadow: 
        0 20px 40px -5px rgba(46, 89, 144, 0.3),
        0 10px 20px -5px rgba(46, 89, 144, 0.2);
    position: relative;
    overflow: hidden;
    height: 100%; /* Changed from 'fit-content' to '100%' */
    display: flex;
    flex-direction: column;
    justify-content: space-between; /* Distribute content evenly */
}

.venue-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(255, 140, 66, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(76, 175, 80, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.venue-info-card > * {
    position: relative;
    z-index: 2;
}

/* Venue Card Header */
.venue-card-header {
    margin-bottom: 2.5rem;
    text-align: left;
}

.venue-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: white;
    margin-bottom: 1rem;
    letter-spacing: 0.5px;
}

.venue-title-underline {
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-orange), #FFB366);
    border-radius: 2px;
}

/* Address Section */
.venue-address-section {
    margin-bottom: 2.5rem;
}

.address-item {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.address-icon {
    color: var(--primary-orange);
    font-size: 1.8rem;
    margin-top: 0.2rem;
    flex-shrink: 0;
}

.address-content {
    flex: 1;
}

.venue-name {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    color: white;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.venue-address {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.5;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-top: 1.5rem;
    padding: 1rem; /* Add padding for better spacing */
    background: rgba(255, 255, 255, 0.05); /* Change from white to transparent */
    border-radius: 8px; /* Add border radius */
    transition: all 0.3s ease; /* Add transition */
}

.contact-item:hover {
    background: rgba(255, 255, 255, 0.1); /* Slightly more visible on hover */
}

.contact-icon {
    color: var(--primary-orange);
    font-size: 1.6rem;
    flex-shrink: 0;
}

.contact-number {
    font-size: var(--font-size-base);
    color: white;
    font-weight: var(--font-weight-medium);
    text-decoration: none;
}   

/* Directions Container */
.directions-container {
    margin-bottom: 2.5rem;
}

.directions-heading {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: white;
    margin-bottom: 1.5rem;
    position: relative;
}

.directions-heading::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--primary-orange);
    border-radius: 1px;
}

.direction-list {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
}

.direction-row {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.direction-row:hover {
    background: rgba(255, 255, 255, 0.1);
    border-left-color: var(--primary-orange);
    transform: translateX(5px);
}

.direction-icon-wrapper {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 1.4rem;
    color: white;
}

.airport-icon {
    background: linear-gradient(135deg, #4CAF50, #66BB6A);
}

.railway-icon {
    background: linear-gradient(135deg, #FF8C42, #FFB366);
}

.bus-icon {
    background: linear-gradient(135deg, #2E5990, #4A7BC8);
}

.direction-text {
    flex: 1;
}

.direction-from {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: white;
    margin-bottom: 0.3rem;
}

.direction-distance {
    display: block;
    font-size: var(--font-size-xs);
    color: rgba(255, 255, 255, 0.8);
}

/* Facilities Container */
.facilities-container {
    margin-bottom: 2.5rem;
}

.facilities-heading {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: white;
    margin-bottom: 1.5rem;
    position: relative;
}

.facilities-heading::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 30px;
    height: 2px;
    background: var(--primary-orange);
    border-radius: 1px;
}

.facilities-icons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.facility-icon-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    transition: all 0.3s ease;
}

.facility-icon-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.facility-icon-item i {
    color: var(--primary-orange);
    font-size: 1.4rem;
    width: 20px;
    text-align: center;
}

.facility-icon-item span {
    font-size: var(--font-size-sm);
    color: white;
    font-weight: var(--font-weight-medium);
}

/* Get Directions Button */
.directions-button-container {
    text-align: center;
}

.get-directions-button {
    display: inline-flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem 2.5rem;
    background: linear-gradient(135deg, var(--primary-orange), #FFB366);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 
        0 8px 20px -5px rgba(255, 140, 66, 0.4),
        0 4px 10px -3px rgba(255, 140, 66, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.get-directions-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.get-directions-button:hover::before {
    left: 100%;
}

.get-directions-button:hover {
    transform: translateY(-3px);
    box-shadow: 
        0 12px 30px -5px rgba(255, 140, 66, 0.5),
        0 8px 15px -3px rgba(255, 140, 66, 0.4);
}

.get-directions-button i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.get-directions-button:hover i {
    transform: rotate(90deg);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .venue-grid {
        gap: 3rem;
    }
    
    .venue-info-card {
        padding: 2.5rem;
    }
    
    .venue-main-title {
        font-size: var(--font-size-2xl);
    }
}

@media (max-width: 1024px) {
    .venue-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
    
    .venue-section {
        padding: 6rem 0;
    }
    
    .venue-header-center {
        margin-bottom: 4rem;
    }
    
    .map-container iframe {
        height: 400px;
    }
}

@media (max-width: 768px) {
    .venue-section {
        padding: 4rem 0;
    }
    
    .venue-header-center {
        margin-bottom: 3rem;
    }
    
    .venue-main-title {
        font-size: var(--font-size-xl);
    }
    
    .venue-info-card {
        padding: 2rem;
    }
    
    .venue-title {
        font-size: var(--font-size-xl);
    }
    
    .direction-row {
        padding: 0.8rem;
    }
    
    .direction-icon-wrapper {
        width: 35px;
        height: 35px;
        font-size: 1.2rem;
    }
    
    .facilities-icons {
        grid-template-columns: 1fr;
    }
    
    .map-container iframe {
        height: 350px;
    }
}

@media (max-width: 480px) {
    .venue-section {
        padding: 3rem 0;
    }
    
    .venue-header-center {
        margin-bottom: 2.5rem;
    }
    
    .venue-main-title {
        font-size: var(--font-size-lg);
    }
    
    .venue-subtitle {
        font-size: var(--font-size-base);
    }
    
    .venue-info-card {
        padding: 1.5rem;
    }
    
    .venue-title {
        font-size: var(--font-size-lg);
    }
    
    .address-item {
        gap: 1rem;
    }
    
    .direction-row {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .direction-icon-wrapper {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }
    
    .facility-icon-item {
        padding: 0.6rem;
    }
    
    .get-directions-button {
        padding: 0.8rem 2rem;
        font-size: var(--font-size-xs);
    }
    
    .map-container {
        padding: 0.5rem;
    }
    
    .map-container iframe {
        height: 300px;
    }
}

/* ========================================
   Footer Section Styles
   ======================================== */

.footer-section {
    position: relative;
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 50%, var(--primary-green-dark) 100%);
    padding: 6rem 0 2rem;
    margin-top: 8rem;
    overflow: hidden;
    min-height: 500px;
    width: 100%;
    box-sizing: border-box;
}

/* Beautiful Dotted Background Pattern */
.footer-dotted-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 1px 1px, rgba(255, 255, 255, 0.15) 1px, transparent 0),
        radial-gradient(circle at 20px 20px, rgba(255, 140, 66, 0.1) 1px, transparent 0),
        radial-gradient(circle at 40px 40px, rgba(76, 175, 80, 0.1) 1px, transparent 0);
    background-size: 20px 20px, 40px 40px, 60px 60px;
    background-position: 0 0, 10px 10px, 20px 20px;
    opacity: 0.7;
    animation: backgroundMove 30s ease-in-out infinite;
}

@keyframes backgroundMove {
    0%, 100% {
        background-position: 0 0, 10px 10px, 20px 20px;
    }
    50% {
        background-position: 20px 20px, 30px 30px, 40px 40px;
    }
}

.footer-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, 
        var(--primary-orange) 0%, 
        var(--primary-green) 25%, 
        var(--primary-blue-light) 50%, 
        var(--primary-green) 75%, 
        var(--primary-orange) 100%);
    animation: shimmerLine 3s ease-in-out infinite;
}

@keyframes shimmerLine {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1.5fr 1.5fr;
    gap: 3rem;
    position: relative;
    z-index: 2;
}

/* Footer Brand Section */
.footer-brand {
    padding-right: 2rem;
}

.footer-logo-container {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.footer-logo {
    width: 6rem;
    height: 6rem;
    border-radius: var(--radius-full);
    background: #ffffff;
    padding: 0.5rem;
    transition: all var(--transition-normal);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.footer-logo:hover {
    transform: rotate(360deg) scale(1.1);
    background: #ffffff;
    box-shadow: 0 0 20px rgba(255, 140, 66, 0.5), 0 4px 20px rgba(0, 0, 0, 0.15);
}

.footer-brand-text {
    color: var(--white);
}

.footer-conference-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    background: linear-gradient(135deg, var(--primary-orange), var(--primary-green-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.footer-conference-subtitle {
    font-size: var(--font-size-sm);
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
}

.footer-conference-date {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-orange);
}

.footer-description {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.8;
    margin-bottom: 2.5rem;
    font-size: var(--font-size-sm);
}

/* Footer Social Links */
.footer-social-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.footer-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 4.5rem;
    height: 4.5rem;
    border-radius: var(--radius-full);
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    text-decoration: none;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.footer-social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.footer-social-link:hover::before {
    left: 100%;
}

.footer-social-link.facebook:hover {
    background: #1877F2;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(24, 119, 242, 0.3);
}

.footer-social-link.twitter:hover {
    background: #1DA1F2;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(29, 161, 242, 0.3);
}

.footer-social-link.linkedin:hover {
    background: #0A66C2;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(10, 102, 194, 0.3);
}

.footer-social-link.instagram:hover {
    background: linear-gradient(45deg, #F56040, #E1306C, #C13584);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(225, 48, 108, 0.3);
}

.footer-social-link.youtube:hover {
    background: #FF0000;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 0, 0, 0.3);
}

.footer-social-link i {
    font-size: 1.8rem;
    transition: all var(--transition-normal);
}

.footer-social-link:hover i {
    transform: scale(1.2);
}

/* Footer Columns */
.footer-column {
    position: relative;
}

.footer-column-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    color: var(--white);
    margin-bottom: 2rem;
    position: relative;
    padding-bottom: 1rem;
}

.footer-column-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-orange), var(--primary-green));
    border-radius: 1px;
}

.footer-column-title i {
    color: var(--primary-orange);
    font-size: 1.6rem;
}

/* Footer Links */
.footer-links {
    list-style: none;
    padding: 0;
}

.footer-links li {
    margin-bottom: 1rem;
    position: relative;
    padding-left: 1.5rem;
}

.footer-links li::before {
    content: '▸';
    position: absolute;
    left: 0;
    color: var(--primary-orange);
    transition: all var(--transition-normal);
    transform: translateX(0);
}

.footer-links li:hover::before {
    transform: translateX(5px);
    color: var(--primary-green-light);
}

.footer-link {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: all var(--transition-normal);
    position: relative;
}

.footer-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--primary-orange);
    transition: width var(--transition-normal);
}

.footer-link:hover {
    color: var(--white);
    transform: translateX(5px);
}

.footer-link:hover::after {
    width: 100%;
}

/* Footer Contact Info */
.footer-contact-info {
    space-y: 2rem;
}

.footer-contact-item {
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-lg);
    border-left: 3px solid var(--primary-orange);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.footer-contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 140, 66, 0.1), transparent);
    transition: left 0.5s;
}

.footer-contact-item:hover::before {
    left: 100%;
}

.footer-contact-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-left-color: var(--primary-green);
}

.contact-person {
    position: relative;
    z-index: 2;
}

.person-name {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    color: var(--white);
    margin-bottom: 0.5rem;
}

.person-title {
    font-size: var(--font-size-sm);
    color: var(--primary-orange);
    margin-bottom: 1rem;
    font-weight: var(--font-weight-medium);
}

.person-contact {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.contact-detail {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: var(--font-size-xs);
    transition: all var(--transition-normal);
}

.contact-detail:hover {
    color: var(--white);
    transform: translateX(5px);
}

.contact-detail i {
    color: var(--primary-green);
    font-size: 1.4rem;
    width: 2rem;
    transition: all var(--transition-normal);
}

.contact-detail:hover i {
    color: var(--primary-orange);
    transform: scale(1.1);
}

/* Footer General Contact */
.footer-general-contact {
    margin-top: 2rem;
    padding: 1.5rem;
    background: rgba(76, 175, 80, 0.1);
    border-radius: var(--radius-lg);
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.general-contact-title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--primary-green-light);
    margin-bottom: 1rem;
}

.general-contact-details {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

/* Organiser Info */
.organiser-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.organiser-logo {
    text-align: left;
}

.organiser-logo-img {
    width: 8rem;
    height: 8rem;
    border-radius: var(--radius-full);
    background: #ffffff;
    padding: 1rem;
    transition: all var(--transition-normal);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.organiser-logo-img:hover {
    transform: scale(1.1) rotate(5deg);
    background: #ffffff;
    box-shadow: 0 0 30px rgba(255, 140, 66, 0.3), 0 4px 20px rgba(0, 0, 0, 0.15);
}

.organiser-details {
    text-align: left;
}

.organiser-name {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    color: var(--white);
    margin-bottom: 0.5rem;
}

.organiser-subtitle {
    font-size: var(--font-size-sm);
    color: var(--primary-orange);
    margin-bottom: 1rem;
    font-style: italic;
}

.organiser-contact {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin: 2rem 0;
}

.organiser-address,
.organiser-email,
.organiser-phone {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 1rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    font-size: var(--font-size-xs);
    transition: all var(--transition-normal);
    padding: 0.5rem 0;
}

.organiser-email:hover,
.organiser-phone:hover {
    color: var(--white);
    transform: translateX(5px);
}

.organiser-address i,
.organiser-email i,
.organiser-phone i {
    color: var(--primary-green);
    font-size: 1.4rem;
    margin-top: 0.1rem;
    flex-shrink: 0;
    width: 1.6rem;
    text-align: center;
}

/* Certification Badges */
.certification-badges {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 2rem;
}

.badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    background: rgba(255, 140, 66, 0.1);
    border: 1px solid rgba(255, 140, 66, 0.3);
    border-radius: var(--radius-lg);
    color: var(--primary-orange);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-normal);
}

.badge:hover {
    background: rgba(255, 140, 66, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 140, 66, 0.2);
}

.badge i {
    font-size: 1.2rem;
}

/* Footer Bottom */
.footer-bottom {
    /* margin-top: 4rem; */
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2rem;
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--font-size-xs);
}

.footer-copyright p {
    margin-bottom: 0.5rem;
}

.developer-link {
    color: var(--primary-orange);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.developer-link:hover {
    color: var(--primary-green-light);
    text-decoration: underline;
}

.footer-legal-links {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.legal-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: var(--font-size-xs);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.legal-link:hover {
    color: var(--white);
    text-decoration: underline;
}

.legal-separator {
    color: rgba(255, 255, 255, 0.5);
}

/* ========================================
   Footer Responsive Styles - Enhanced
   ======================================== */

@media (max-width: 1200px) {
    .footer-content {
        grid-template-columns: 2fr 1fr 1.2fr 1.2fr;
        gap: 2.5rem;
    }
    
    .footer-brand {
        padding-right: 0.5rem;
    }
    
    .footer-description {
        font-size: var(--font-size-sm);
        line-height: 1.7;
    }
    
    .footer-conference-title {
        font-size: var(--font-size-lg);
    }
}

/* Specific styles for 1024x1366 and similar resolutions */
@media (max-width: 1024px) and (min-width: 769px) {
    /* Ensure proper page height handling */
    html, body {
        min-height: 100vh;
        overflow-x: hidden;
    }

    .footer-section {
        padding: 5rem 0 2rem;
        margin-top: 6rem;
        min-height: 400px;
        width: 100%;
        position: relative;
        clear: both;
    }

    .footer-content {
        grid-template-columns: 1fr 1fr 1fr;
        gap: 2rem;
        max-width: 1000px;
        margin: 0 auto;
    }

    .footer-brand {
        grid-column: 1 / -1;
        padding-right: 0;
        margin-bottom: 2rem;
        text-align: center;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
    }

    .footer-logo-container {
        justify-content: center;
        text-align: center;
    }

    .footer-description {
        text-align: center;
        max-width: 600px;
        margin: 0 auto 2.5rem auto;
        font-size: var(--font-size-sm);
        line-height: 1.8;
    }

    .footer-social-links {
        justify-content: center;
    }

    .footer-column {
        text-align: left;
    }

    .footer-column-title {
        font-size: 1.6rem;
        margin-bottom: 1.5rem;
    }

    .organiser-info {
        align-items: flex-start;
        text-align: left;
    }

    .organiser-logo {
        text-align: left;
        margin-bottom: 1rem;
    }

    .organiser-details {
        text-align: left;
    }

    .rx-events-logo {
        width: 85px;
        height: auto;
    }

    /* Footer bottom section for 1024x1366 */
    .footer-bottom {
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .footer-bottom-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
        max-width: 1000px;
        margin: 0 auto;
    }

    .footer-copyright {
        text-align: left;
    }

    .footer-legal-links {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    /* Ensure container has proper padding for 1024x1366 */
    .container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 2rem;
    }
}

/* Tablet Portrait and smaller */
@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2.5rem;
    }

    .footer-brand {
        grid-column: 1 / -1;
        padding-right: 0;
        margin-bottom: 2rem;
        text-align: center;
    }

    .footer-logo-container {
        justify-content: center;
        text-align: center;
    }

    .footer-description {
        text-align: center;
        max-width: 600px;
        margin: 0 auto 2.5rem auto;
        font-size: var(--font-size-sm);
        line-height: 1.8;
    }

    .footer-social-links {
        justify-content: center;
    }

    .organiser-info {
        align-items: flex-start;
        text-align: left;
    }

    .organiser-logo {
        text-align: left;
    }

    .organiser-details {
        text-align: left;
    }
}

/* Mobile and small tablets */
@media (max-width: 768px) {
    .footer-section {
        padding: 4rem 0 2rem;
        margin-top: 6rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: left;
    }

    .footer-brand {
        margin-bottom: 1rem;
        text-align: left;
    }

    .footer-logo-container {
        flex-direction: row;
        gap: 1.2rem;
        justify-content: flex-start;
        text-align: left;
    }

    .footer-logo {
        width: 5rem;
        height: 5rem;
    }

    .footer-brand-text {
        text-align: left;
    }

    .footer-conference-title {
        font-size: var(--font-size-lg);
        margin-bottom: 0.4rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .footer-conference-subtitle {
        font-size: var(--font-size-sm);
        margin-bottom: 0.4rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .footer-conference-date {
        font-size: var(--font-size-sm);
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .footer-description {
        text-align: left;
        font-size: var(--font-size-sm);
        line-height: 1.7;
        margin-bottom: 2rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
        hyphens: auto;
    }
    
    .footer-social-links {
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .footer-social-link {
        width: 4rem;
        height: 4rem;
    }
    
    .footer-social-link i {
        font-size: 1.6rem;
    }
    
    .footer-column-title {
        font-size: var(--font-size-md);
        margin-bottom: 1.8rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .footer-links li {
        padding-left: 1.5rem;
        text-align: left;
        margin-bottom: 0.8rem;
    }
    
    .footer-links li::before {
        display: block;
    }
    
    .footer-link {
        font-size: var(--font-size-sm);
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .footer-contact-item {
        text-align: left;
        padding: 1.2rem;
        margin-bottom: 2rem;
    }
    
    .person-name {
        font-size: var(--font-size-md);
        margin-bottom: 0.4rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .person-title {
        font-size: var(--font-size-sm);
        margin-bottom: 1rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
        line-height: 1.5;
    }
    
    .contact-detail {
        font-size: var(--font-size-sm);
        flex-wrap: wrap;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .organiser-name {
        font-size: var(--font-size-md);
        margin-bottom: 1rem;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .organiser-email,
    .organiser-phone {
        font-size: var(--font-size-sm);
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .certification-badges {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        text-align: left;
        gap: 1.5rem;
        align-items: flex-start;
    }
    
    .footer-copyright {
        font-size: var(--font-size-xs);
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
    
    .footer-legal-links {
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    .legal-link {
        font-size: var(--font-size-xs);
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
}

@media (max-width: 480px) {
    .footer-section {
        padding: 3rem 0 1.5rem;
        margin-top: 0rem;
    }
    
    .footer-content {
        gap: 2.5rem;
    }
    
    .footer-logo-container {
        gap: 1rem;
    }
    
    .footer-logo {
        width: 4.5rem;
        height: 4.5rem;
    }
    
    .footer-conference-title {
        font-size: var(--font-size-md);
        line-height: 1.3;
    }
    
    .footer-conference-subtitle {
        font-size: var(--font-size-xs);
    }
    
    .footer-conference-date {
        font-size: var(--font-size-xs);
    }
    
    .footer-description {
        font-size: var(--font-size-xs);
        line-height: 1.6;
        margin-bottom: 1.8rem;
    }
    
    .footer-column-title {
        font-size: var(--font-size-sm);
        margin-bottom: 1.5rem;
    }
    
    .footer-contact-item {
        padding: 1rem;
        margin-bottom: 1.8rem;
    }
    
    .person-name {
        font-size: var(--font-size-sm);
    }
    
    .person-title {
        font-size: var(--font-size-xs);
        line-height: 1.4;
    }
    
    .contact-detail {
        font-size: var(--font-size-xs);
        gap: 0.8rem;
    }
    
    .contact-detail i {
        font-size: 1.2rem;
        width: 1.8rem;
    }
    
    .organiser-name {
        font-size: var(--font-size-sm);
    }
    
    .organiser-email,
    .organiser-phone {
        font-size: var(--font-size-xs);
    }
    
    .organiser-logo-img {
        width: 6rem;
        height: 6rem;
    }
    
    .rx-events-logo {
        width: 80px;
        height: auto;
    }
    
    .organiser-info {
        align-items: flex-start;
        text-align: left;
    }
    
    .organiser-logo {
        text-align: left;
    }
    
    .organiser-details {
        text-align: left;
    }
    
    .organiser-contact {
        align-items: flex-start;
        gap: 0.8rem;
    }

    .person-contact {
        gap: 0.6rem;
    }
    
    .footer-bottom-content {
        gap: 1.2rem;
        flex-direction: column;
        text-align: center;
        align-items: center;
    }
    
    .footer-copyright p {
        margin-bottom: 0.3rem;
        font-size: var(--font-size-xs);
        text-align: center;
    }
    
    .footer-legal-links {
        gap: 0.8rem;
        justify-content: center;
        flex-wrap: wrap;
    }
}

@media (max-width: 360px) {
    .footer-section {
        padding: 2.5rem 0 1rem;
    }
    .footer-conference-title, .footer-conference-subtitle, .footer-conference-date{
    text-align: center;
}
    .footer-content {
        gap: 2rem;
    }

    .footer-logo-container {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .footer-brand {
        text-align: center;
    }

    .footer-description {
        text-align: center;
    }

    .footer-social-links {
        justify-content: center;
    }

    .footer-logo {
        width: 4rem;
        height: 4rem;
    }

    .footer-conference-title {
        font-size: var(--font-size-sm);
        line-height: 1.2;
    }

    .footer-conference-subtitle,
    .footer-conference-date {
        font-size: var(--font-size-xs);
    }

    .footer-description {
        font-size: var(--font-size-xs);
        line-height: 1.5;
        margin-bottom: 1.5rem;
    }

    .footer-column-title {
        font-size: var(--font-size-xs);
        margin-bottom: 1.2rem;
    }
    
    .footer-links li {
        margin-bottom: 0.6rem;
        padding-left: 1.2rem;
    }

    .footer-link {
        font-size: var(--font-size-xs);
    }

    .footer-contact-item {
        padding: 0.8rem;
        margin-bottom: 1.5rem;
    }

    .person-name {
        font-size: var(--font-size-xs);
    }

    .person-title {
        font-size: var(--font-size-xs);
        line-height: 1.3;
    }

    .contact-detail {
        font-size: var(--font-size-xs);
        gap: 0.6rem;
        flex-direction: column;
        align-items: flex-start;
    }

    .contact-detail i {
        font-size: 1rem;
        width: 1.5rem;
    }

    .organiser-name {
        font-size: var(--font-size-xs);
    }

    .organiser-email,
    .organiser-phone {
        font-size: var(--font-size-xs);
        line-height: 1.3;
    }

    .rx-events-logo {
        width: 70px;
        height: auto;
    }

    .footer-copyright p {
        font-size: var(--font-size-xs);
        line-height: 1.3;
    }

    .footer-legal-links {
        flex-direction: row;
        gap: 0.5rem;
        align-items: center;
    }

    .legal-separator {
        display: none;
    }

    .legal-link {
        font-size: var(--font-size-xs);
    }
}

/* Performance optimization for footer */
.footer-section {
    contain: layout style;
    will-change: transform;
    transform: translateZ(0);
}

.footer-dotted-bg {
    will-change: transform;
    transform: translateZ(0);
}

/* RX Events Logo Specific Styling - Enhanced Responsive */
.rx-events-logo {
    width: 100px;
    height: auto;
    border-radius: var(--radius-md);
    transition: all var(--transition-normal);
    object-fit: contain;
    max-width: 100%;
}

/* Responsive RX Logo */
@media (max-width: 768px) {
    .rx-events-logo {
        width: 90px;
    }
}

@media (max-width: 480px) {
    .rx-events-logo {
        width: 80px;
    }
}

@media (max-width: 360px) {
    .rx-events-logo {
        width: 70px;
    }
}

/* ========================================
   GLOBAL PAGE HERO COMPONENT
   ======================================== */

/* Global Page Hero Section - Universal component for all pages */
.global-page-hero {
    padding: 8rem 0 5rem;
    background: linear-gradient(135deg, #2E5990 0%, #1A3A5C 100%);
    color: white;
    position: relative;
    overflow: hidden;
    margin-top: 14rem !important;
    min-height: 25vh;
    display: flex;
    align-items: center;
    justify-content: center;
    /* Performance Optimization */
    contain: layout style paint;
    will-change: transform;
    transform: translateZ(0);
}

.global-page-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        /* Elegant gradient orbs */
        radial-gradient(circle at 15% 85%, rgba(255, 140, 66, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 85% 15%, rgba(76, 175, 80, 0.06) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.02) 0%, transparent 50%),
        /* Hexagonal pattern */
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.03) 2px, transparent 2px),
        radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.03) 2px, transparent 2px),
        /* Diamond pattern */
        linear-gradient(45deg, rgba(255, 255, 255, 0.01) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(255, 255, 255, 0.01) 25%, transparent 25%),
        /* Medical cross pattern */
        linear-gradient(0deg, transparent 45%, rgba(255, 255, 255, 0.015) 50%, transparent 55%),
        linear-gradient(90deg, transparent 45%, rgba(255, 255, 255, 0.015) 50%, transparent 55%);
    background-size: 
        400px 400px,
        350px 350px,
        300px 300px,
        80px 80px,
        80px 80px,
        40px 40px,
        40px 40px,
        120px 120px,
        120px 120px;
    background-position: 
        0% 0%,
        100% 100%,
        50% 50%,
        0 0,
        40px 40px,
        0 0,
        20px 20px,
        0 0,
        0 0;
    pointer-events: none;
}

.global-page-hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        /* Subtle dot pattern */
        radial-gradient(circle, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
        /* Medical plus signs */
        radial-gradient(circle at 25% 25%, rgba(46, 89, 144, 0.02) 3px, transparent 3px),
        radial-gradient(circle at 75% 75%, rgba(255, 140, 66, 0.02) 3px, transparent 3px);
    background-size: 
        50px 50px,
        200px 200px,
        200px 200px;
    background-position: 
        0 0,
        0 0,
        100px 100px;
    opacity: 0.4;
    pointer-events: none;
}



.global-page-hero .container {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 100rem;
    margin: 0 auto;
    padding: 0 2rem;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Global Hero Content - Perfect Centering */
.global-hero-content {
    max-width: 80rem;
    margin: 0 auto;
    word-wrap: break-word;
    overflow-wrap: break-word;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    z-index: 3;
}

/* Global Hero Title */
.global-hero-title {
    font-family: var(--font-family-primary);
    font-size: 4.5rem;
    font-weight: 800;
    margin-bottom: 2rem;
    line-height: 1.1;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    color: white;
    text-align: center;
    position: relative;
    z-index: 2;
    /* text-shadow: 0 3px 6px rgba(0, 0, 0, 0.2); */
    background: linear-gradient(135deg, #FF8C42 0%, #FFB84D 50%, #FF8C42 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-transform: uppercase;
    letter-spacing: 2px;
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding: 0 1rem;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Global Hero Subtitle */
.global-hero-subtitle {
    font-family: var(--font-family-primary);
    font-size: 1.8rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 3rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: 1px;
    line-height: 1.4;
    max-width: 60rem;
    margin: 0 auto 3rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    text-align: center;
    padding: 0 1rem;
}

/* Global Hero Badge/CTA */
.global-hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.5rem 3rem;
    border-radius: var(--radius-full);
    font-family: var(--font-family-primary);
    font-size: 1.4rem;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    margin-top: 2rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    text-align: center;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.3;
}

.global-hero-badge:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.global-hero-badge i {
    font-size: 1.4rem;
    color: var(--primary-orange);
}

/* Quick Info Section (Optional) */
.global-hero-quick-info {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3rem;
    margin-top: 4rem;
    flex-wrap: wrap;
}

.global-quick-info-item {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    padding: 1.2rem 2rem;
    border-radius: var(--radius-xl);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.15);
    min-width: 18rem;
}

.global-quick-info-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.global-quick-info-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.5rem;
    height: 3.5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    flex-shrink: 0;
}

.global-quick-info-icon i {
    font-size: 1.6rem;
    color: white;
}

.global-quick-info-text {
    font-family: var(--font-family-primary);
    font-size: 1.4rem;
    font-weight: 600;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ========================================
   Chief Patron Highlighting Styles
   ======================================== */

/* Chief Patron Section Styling */
.chief-patron-section {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
    padding: 0 20px;
    margin-top: 2rem;
    margin-bottom: 6rem;
}

.chief-patron-card {
    transform: scale(1.1) !important;
    position: relative;
    border: 2px solid #2e5990 !important;
    box-shadow: var(--shadow-md) !important;
    background: #fff !important;
}

.chief-patron-card:hover {
    transform: scale(1.1) translateY(-3px) !important;
    box-shadow: var(--shadow-lg) !important;
}

.chief-patron-card .member-name {
    font-size: 1.8rem !important;
    background: linear-gradient(135deg, #2e5990 0%, #ff8c42 50%, #2e5990 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.chief-patron-card .member-title {
    color: #ff8c42 !important;
    font-weight: 600 !important;
    font-size: 1.4rem !important;
}

/* Enhanced image container for chief patron - removed rotating animation */
.chief-patron-card .member-image-container::before {
    background: linear-gradient(135deg, #2e5990, #ff8c42) !important;
    opacity: 0.8 !important;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .chief-patron-section {
        padding: 0 1.5rem;
        margin-bottom: 4rem;
    }
}

@media (max-width: 768px) and (min-width: 541px) {
    .chief-patron-section {
        padding: 0 2rem;
        margin-bottom: 4rem;
    }
    
    .chief-patron-card {
        width: 100% !important;
        max-width: 320px !important;
        min-width: 280px !important;
        margin: 0 auto !important;
        transform: scale(1.05) !important;
    }
    
    .chief-patron-card:hover {
        transform: scale(1.05) translateY(-3px) !important;
    }
    
    .chief-patron-card .member-name {
        font-size: 1.6rem !important;
    }

    .chief-patron-card .member-title {
        font-size: 1.3rem !important;
    }
}

@media (max-width: 540px) and (min-width: 481px) {
    .chief-patron-section {
        padding: 0 1.5rem;
        margin-bottom: 4rem;
    }
    
    .chief-patron-card {
        width: 100% !important;
        max-width: 350px !important;
        min-width: 300px !important;
        margin: 0 auto !important;
        transform: scale(1.03) !important;
    }
    
    .chief-patron-card:hover {
        transform: scale(1.03) translateY(-3px) !important;
    }
    
    .chief-patron-card .member-name {
        font-size: 1.5rem !important;
    }

    .chief-patron-card .member-title {
        font-size: 1.25rem !important;
    }
}

@media (max-width: 480px) {
    .chief-patron-section {
        padding: 0 1.5rem;
        margin-bottom: 3rem;
    }
    
    .chief-patron-card {
        width: 100% !important;
        max-width: 300px !important;
        min-width: 280px !important;
        margin: 0 auto !important;
        transform: scale(1.02) !important;
    }
    
    .chief-patron-card:hover {
        transform: scale(1.02) translateY(-2px) !important;
    }
    
    .chief-patron-card .member-name {
        font-size: 1.4rem !important;
    }

    .chief-patron-card .member-title {
        font-size: 1.2rem !important;
    }
}

@media (max-width: 360px) {
    .chief-patron-section {
        padding: 0 1rem;
        margin-bottom: 3rem;
    }
    
    .chief-patron-card {
        width: 100% !important;
        max-width: 280px !important;
        min-width: 260px !important;
        margin: 0 auto !important;
        transform: scale(1.0) !important;
    }
    
    .chief-patron-card:hover {
        transform: scale(1.0) translateY(-2px) !important;
    }
    
    .chief-patron-card .member-name {
        font-size: 1.3rem !important;
    }

    .chief-patron-card .member-title {
        font-size: 1.1rem !important;
    }
}

/* Downloads Modal CSS */
    .downloads-modal {
        display: none;
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(5px);
        opacity: 0;
        transition: opacity 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .modal-content {
        background-color: #fff;
        margin: 2% auto;
        padding: 0;
        border-radius: 12px;
        width: 90%;
        max-width: 900px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        animation: modalSlideIn 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        background: linear-gradient(120deg, #2e5990, #5c8ebf);
        color: white;
        padding: 20px 30px;
        border-radius: 12px 12px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-title {
        margin: 0;
        font-size: 2.2rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .modal-close {
        font-size: 3rem;
        font-weight: bold;
        cursor: pointer;
        opacity: 0.8;
        transition: opacity 0.3s;
        line-height: 1;
        padding: 5px;
    }

    .modal-close:hover {
        opacity: 1;
        transform: scale(1.1);
    }

    .modal-body {
        padding: 30px;
    }

    .downloads-grid {
        display: grid;
        gap: 20px;
        margin-bottom: 25px;
    }

    .download-item {
        display: flex;
        align-items: center;
        gap: 20px;
        padding: 20px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        transition: all 0.3s ease;
        background: #fff;
    }

    .download-item:hover {
        border-color: #2e5990;
        box-shadow: 0 8px 20px rgba(46, 89, 144, 0.1);
        transform: translateY(-2px);
    }

    .download-item.coming-soon {
        opacity: 0.6;
        background: #f8f9fa;
    }

    .download-item-icon {
        flex-shrink: 0;
        width: 60px;
        height: 60px;
        background: linear-gradient(120deg, #2e5990, #5c8ebf);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2.4rem;
    }

    .download-item.coming-soon .download-item-icon {
        background: #6c757d;
    }

    .download-item-info {
        flex: 1;
    }

    .download-item-title {
        font-size: 1.8rem;
        font-weight: 600;
        margin: 0 0 8px 0;
        color: #2e5990;
    }

    .download-item.coming-soon .download-item-title {
        color: #6c757d;
    }

    .download-item-description {
        font-size: 1.4rem;
        color: #666;
        margin: 0 0 12px 0;
        line-height: 1.5;
    }

    .download-item-details {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
    }

    .file-size, .file-date, .file-status {
        font-size: 1.2rem;
        color: #777;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .download-item-action {
        flex-shrink: 0;
    }

    .download-item-btn {
        display: inline-flex;
        align-items: center;
        gap: 10px;
        background: linear-gradient(120deg, #ff8c42, #ff6b35);
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 600;
        font-size: 1.4rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .download-item-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 15px rgba(255, 140, 66, 0.3);
    }

    .download-item-btn.disabled {
        background: #6c757d;
        cursor: not-allowed;
    }

    .download-item-btn.disabled:hover {
        transform: none;
        box-shadow: none;
    }

    .downloads-note {
        background: #e7f3ff;
        border: 1px solid #b3d9ff;
        border-radius: 8px;
        padding: 15px 20px;
        color: #2e5990;
        font-size: 1.3rem;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .modal-content {
            width: 95%;
            margin: 5% auto;
        }

        .modal-header {
            padding: 15px 20px;
        }

        .modal-title {
            font-size: 1.8rem;
        }

        .modal-close {
            font-size: 2.5rem;
        }

        .modal-body {
            padding: 20px;
        }

        .download-item {
            flex-direction: column;
            text-align: center;
            gap: 15px;
        }

        .download-item-details {
            justify-content: center;
        }
    }

/* Global Fix for Footer Links Clickability - ALL PAGES */
footer.footer-section {
    position: relative !important;
    z-index: 10 !important;
    pointer-events: auto !important;
}

footer.footer-section a,
footer.footer-section .legal-link,
footer.footer-section .footer-link,
footer.footer-section .developer-link,
footer.footer-section .organiser-email,
footer.footer-section .organiser-phone,
footer.footer-section .contact-detail,
footer.footer-section .footer-social-link {
    pointer-events: auto !important;
    z-index: 11 !important;
    position: relative !important;
    cursor: pointer !important;
}

/* Ensure footer bottom section is clickable */
.footer-bottom,
.footer-legal-links,
.footer-copyright {
    pointer-events: auto !important;
    z-index: 11 !important;
    position: relative !important;
}

/* Gallery Modal CSS */
.gallery-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.gallery-modal .modal-content {
    background-color: #fff;
    margin: 2% auto;
    padding: 0;
    border-radius: 15px;
    width: 95%;
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
    animation: modalSlideIn 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.gallery-categories {
    display: grid;
    gap: 30px;
    margin-bottom: 20px;
}

.gallery-category {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.gallery-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.category-header {
    background: linear-gradient(135deg, #2e5990, #4a7ba7);
    color: white;
    padding: 25px 30px;
    position: relative;
    overflow: hidden;
}

.category-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.gallery-category:hover .category-header::before {
    left: 100%;
}

.category-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    gap: 15px;
}

.category-title i {
    font-size: 2.8rem;
    opacity: 0.9;
}

.category-description {
    font-size: 1.4rem;
    margin: 0;
    opacity: 0.9;
    line-height: 1.5;
}

.gallery-preview {
    padding: 30px;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.preview-item {
    aspect-ratio: 4/3;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preview-item:hover {
    /* Simple hover effect */
}

.preview-item.coming-soon {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px dashed #dee2e6;
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(46, 89, 144, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.preview-item:hover .preview-overlay {
    opacity: 1;
}

.preview-overlay i {
    font-size: 2.5rem;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.3));
}

.preview-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    font-size: 1.6rem;
    font-weight: 500;
    gap: 10px;
}

.preview-placeholder i {
    font-size: 3rem;
    opacity: 0.6;
}

.preview-placeholder span {
    font-size: 1.3rem;
    opacity: 0.8;
}

.view-all-btn {
    width: 100%;
    padding: 15px 25px;
    background: linear-gradient(135deg, #2e5990, #4a7ba7);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1.6rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.view-all-btn:hover {
    background: linear-gradient(135deg, #1e3f70, #3a6b97);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(46, 89, 144, 0.3);
}

.view-all-btn.disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.7;
}

.view-all-btn.disabled:hover {
    transform: none;
    box-shadow: none;
    background: #6c757d;
}

.gallery-note {
    background: linear-gradient(135deg, #e7f3ff, #f0f8ff);
    border: 1px solid #b3d9ff;
    border-radius: 10px;
    padding: 20px;
    color: #2e5990;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 20px;
}

.gallery-note i {
    font-size: 2rem;
    opacity: 0.8;
}

/* Responsive Gallery Modal */
@media (max-width: 1024px) {
    .gallery-modal .modal-content {
        width: 90%;
        margin: 3% auto;
    }
    
    .category-title {
        font-size: 2rem;
    }
    
    .category-title i {
        font-size: 2.4rem;
    }
    
    .preview-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .gallery-modal .modal-content {
        width: 95%;
        margin: 5% auto;
    }
    
    .category-header {
        padding: 20px;
    }
    
    .category-title {
        font-size: 1.8rem;
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .category-title i {
        font-size: 2.2rem;
    }
    
    .category-description {
        font-size: 1.3rem;
        text-align: center;
    }
    
    .gallery-preview {
        padding: 20px;
    }
    
    .preview-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
    }
    
    .preview-placeholder {
        font-size: 1.4rem;
    }
    
    .preview-placeholder i {
        font-size: 2.5rem;
    }
    
    .view-all-btn {
        font-size: 1.4rem;
        padding: 12px 20px;
    }
    
    .gallery-note {
        font-size: 1.3rem;
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .gallery-modal .modal-content {
        width: 95%;
        margin: 8% auto;
    }
    
    .category-header {
        padding: 15px;
    }
    
    .category-title {
        font-size: 1.6rem;
    }
    
    .category-title i {
        font-size: 2rem;
    }
    
    .category-description {
        font-size: 1.2rem;
    }
    
    .gallery-preview {
        padding: 15px;
    }
    
    .preview-grid {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
    }
    
    .preview-placeholder {
        font-size: 1.2rem;
    }
    
    .preview-placeholder i {
        font-size: 2rem;
    }
    
    .preview-placeholder span {
        font-size: 1.1rem;
    }
    
    .view-all-btn {
        font-size: 1.3rem;
        padding: 10px 15px;
    }
    
    .gallery-note {
        font-size: 1.2rem;
        padding: 12px;
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }
}


/* Gallery Page Styles */
.gallery-page {
    min-height: 100vh;
    padding: 2rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.gallery-hero {
    text-align: center;
    padding: 3rem 0;
    background: linear-gradient(135deg, #2e5990, #4a7ba7);
    color: white;
    border-radius: 15px;
    margin-bottom: 4rem;
    box-shadow: 0 10px 30px rgba(46, 89, 144, 0.3);
}

.gallery-hero h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.gallery-hero h1 i {
    font-size: 2.5rem;
    opacity: 0.9;
}

.gallery-hero p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.gallery-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    line-height: 1;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Gallery Grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    padding: 2rem 0;
}

.gallery-item {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

.gallery-item-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(46, 89, 144, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-item-overlay {
    opacity: 1;
}

.gallery-item-overlay i {
    font-size: 2rem;
    color: white;
}

.gallery-item-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 2rem 1rem 1rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-item-info {
    transform: translateY(0);
}

.gallery-item-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

/* Lightbox Styles */
.lightbox {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(5px);
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    margin: auto;
    display: block;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 10px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.lightbox-close {
    position: absolute;
    top: 20px;
    right: 35px;
    color: white;
    font-size: 4rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    transition: opacity 0.3s ease;
}

.lightbox-close:hover {
    opacity: 0.7;
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    font-size: 1.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.lightbox-nav:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

.lightbox-prev {
    left: 30px;
}

.lightbox-next {
    right: 30px;
}

.lightbox-counter {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    background: rgba(0, 0, 0, 0.7);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 1rem;
    backdrop-filter: blur(10px);
}

/* Responsive Design for Gallery Page */
@media (max-width: 1024px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }
    
    .gallery-hero h1 {
        font-size: 2.5rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .gallery-page {
        padding: 1rem 0;
    }
    
    .gallery-hero {
        padding: 2rem 1rem;
        margin-bottom: 2rem;
    }
    
    .gallery-hero h1 {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .gallery-hero h1 i {
        font-size: 2rem;
    }
    
    .gallery-stats {
        gap: 2rem;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
        padding: 1rem 0;
    }
    
    .gallery-item img {
        height: 200px;
    }
    
    .lightbox-close {
        top: 10px;
        right: 20px;
        font-size: 3rem;
    }
    
    .lightbox-nav {
        padding: 0.8rem 1rem;
        font-size: 1.2rem;
    }
    
    .lightbox-prev {
        left: 15px;
    }
    
    .lightbox-next {
        right: 15px;
    }
}

@media (max-width: 480px) {
    .gallery-hero h1 {
        font-size: 1.8rem;
    }
    
    .gallery-hero p {
        font-size: 1.1rem;
    }
    
    .gallery-stats {
        gap: 1.5rem;
    }
    
    .stat-number {
        font-size: 1.8rem;
    }
    
    .stat-label {
        font-size: 0.9rem;
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 0.8rem;
    }
    
    .gallery-item img {
        height: 150px;
    }
    
    .gallery-item-title {
        font-size: 1rem;
    }
    
    .lightbox-close {
        font-size: 2.5rem;
        top: 5px;
        right: 15px;
    }
    
    .lightbox-nav {
        padding: 0.6rem 0.8rem;
        font-size: 1rem;
    }
    
    .lightbox-prev {
        left: 10px;
    }
    
    .lightbox-next {
        right: 10px;
    }
    
    .lightbox-counter {
        bottom: 15px;
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }
}



